import json
from datetime import timedelta, datetime, timezone as dt_timezone
import pytz

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.utils import timezone

# --- Adjust these import paths to match your project structure ---
from locations.models import StayPoint


def __get_staypoints_as_geo<PERSON><PERSON>(user, start_date, end_date):
    """
    Helper function to query and format staypoints into GeoJSON,
    making sure datetimes are converted to the user's local timezone.
    """
    profile = user.profiles.first()

    # Get user's timezone. Fallback to project's default timezone.
    user_tz_name = getattr(profile, 'timezone',
                           timezone.get_current_timezone_name())
    try:
        user_tz = pytz.timezone(user_tz_name)
    except pytz.UnknownTimeZoneError:
        user_tz = timezone.get_current_timezone()

    # Query the database for staypoints within the UTC date range
    staypoints = StayPoint.objects.filter(
        profile=profile,
        start_time__gte=start_date,
        end_time__lte=end_date
    ).select_related('place').order_by('-start_time')

    features = []
    for sp in staypoints:
        if sp.point:
            # Convert UTC datetimes from DB to user's local timezone
            local_arrival = timezone.localtime(sp.start_time, user_tz)
            local_departure = timezone.localtime(sp.end_time, user_tz)
            duration = (sp.end_time - sp.start_time).total_seconds() / 60

            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [sp.point.x, sp.point.y]
                },
                "properties": {
                    "place_name": sp.place.name if sp.place else "--",
                    # Send the localized time as a full ISO string
                    "arrival": local_arrival.isoformat(),
                    "departure": local_departure.isoformat(),
                    "duration_minutes": round(duration)
                }
            })

    return {
        "type": "FeatureCollection",
        "features": features
    }


@login_required
def staypoint_map_view(request):
    """
    View for the initial map page load.
    """
    # These dates are naive initially
    today = timezone.now().date()
    start_date = today - timedelta(days=6)
    end_date = today + timedelta(days=1)

    # Make them timezone-aware in UTC for the query
    utc_start = timezone.make_aware(datetime.combine(
        start_date, datetime.min.time()), dt_timezone.utc)
    utc_end = timezone.make_aware(datetime.combine(
        end_date, datetime.min.time()), dt_timezone.utc)

    staypoints_geojson = __get_staypoints_as_geojson(
        request.user, utc_start, utc_end)

    context = {
        'staypoints_geojson': staypoints_geojson
    }
    return render(request, 'locations/staypoint_map.html', context)


@login_required
def staypoint_api_view(request):
    """
    API endpoint for fetching filtered staypoint data via AJAX.
    """
    start_str = request.GET.get('start')
    end_str = request.GET.get('end')

    if not start_str or not end_str:
        return JsonResponse({'error': 'Start and end date parameters are required.'}, status=400)

    try:
        # These dates are naive initially
        start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(
            end_str, '%Y-%m-%d').date() + timedelta(days=1)

        # Make them timezone-aware in UTC for the query
        utc_start = timezone.make_aware(datetime.combine(
            start_date, datetime.min.time()), dt_timezone.utc)
        utc_end = timezone.make_aware(datetime.combine(
            end_date, datetime.min.time()), dt_timezone.utc)

    except (ValueError, TypeError):
        return JsonResponse({'error': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

    staypoints_geojson = __get_staypoints_as_geojson(
        request.user, utc_start, utc_end)

    return JsonResponse(staypoints_geojson)


@login_required
def places_map_view(request):
    """
    View for the places map page.
    """
    return render(request, 'locations/places_map.html')
