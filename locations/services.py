# Assuming this code lives in a file within your Django app, e.g., `myapp/services.py`

import math
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional
from background_task import background
from locations.models import Location, StayPoint
from notifications.services import NotificationsServices
from places.models import Place, PlaceVisit
from django.contrib.gis.geos import Point, GEOSException
from django.contrib.gis.db.models.functions import Distance  # Import Distance function
from django.contrib.gis.measure import D  # Import D for distance measurements
from django.db.models import Q, F
from django.utils import timezone as tz
from django.core.exceptions import ValidationError
from django.db import DatabaseError

from profiles.models import Profile

logger = logging.getLogger(__name__)


# Import necessary Django models and GeoDjango components
# from .models import Location, StayPoint, Profile, Place, PlaceVisit # Adjust import

# --- Haversine Distance Function (Keep, although it's not used for Place finding anymore) ---
# (It might still be useful elsewhere or kept for reference)


@background(schedule=60 * 5)
def process_location_data(profile_id: str, start_time: datetime = None, end_time: datetime = None):
    """Background task to process location data for a profile."""
    try:
        logger.info(
            f"Starting background location processing for profile {profile_id}")

        start_time = start_time or datetime.now(
            tz=timezone.utc) - timedelta(days=1)
        end_time = end_time or datetime.now(tz=timezone.utc)

        logger.debug(
            f"Processing location data from {start_time} to {end_time}")

        result = LocationServices.process_location_data(
            profile_id, start_time, end_time, re_process=True)

        logger.info(
            f"Successfully completed background location processing for profile {profile_id}")
        return result

    except Exception as e:
        logger.error(
            f"Failed to process location data for profile {profile_id}: {str(e)}", exc_info=True)
        raise


def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calculates the great-circle distance between two points on the Earth
    (specified in decimal degrees) using the Haversine formula.
    Returns the distance in meters.
    """
    R = 6371000  # Earth radius in meters
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)

    dlon = lon2_rad - lon1_rad
    dlat = lat2_rad - lat1_rad

    a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * \
        math.cos(lat2_rad) * math.sin(dlon / 2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    distance = R * c
    return distance


class LocationServices:

    @staticmethod
    def find_staypoints_for_user_in_range(
        profile,
        start_time: datetime,
        end_time: datetime,
        spatial_threshold_meters=30,
        temporal_threshold_seconds=60 * 8,    # 8 minutes for standard staypoints
        dwell_temporal_threshold_seconds=60 * 3,  # 3 minutes if DWELL action present
        # 2 minutes if ended by an EXIT action
        exit_defined_min_duration_seconds=60 * 2,
        max_interval_to_associate_exit_seconds=60 * 60,  # 1 hour
        re_process: bool = False,
    ):
        """
        Find staypoints for a user within a time range using spatial clustering and temporal analysis.

        Returns:
            list: List of staypoint dictionaries with location, timing, and classification data
        """
        try:
            logger.info(
                f"Finding staypoints for profile {profile} from {start_time} to {end_time}")
            logger.debug(f"Parameters: spatial_threshold={spatial_threshold_meters}m, "
                         f"temporal_threshold={temporal_threshold_seconds}s, re_process={re_process}")

            # Validate input parameters
            if not profile:
                logger.error("Profile parameter is required")
                raise ValueError("Profile parameter is required")

            if not start_time or not end_time:
                logger.error("Start time and end time are required")
                raise ValueError("Start time and end time are required")

            if start_time >= end_time:
                logger.error(
                    f"Start time ({start_time}) must be before end time ({end_time})")
                raise ValueError("Start time must be before end time")

            # Clean up existing staypoints if reprocessing
            if re_process:
                deleted_count = StayPoint.objects.filter(
                    profile=profile,
                    start_time__gte=start_time,
                    end_time__lte=end_time,
                ).delete()[0]
                logger.info(
                    f"Deleted {deleted_count} existing staypoints for reprocessing")

            # Build query based on processing mode
            location_filter = Q(
                profile=profile,
                timestamp__gte=start_time,
                timestamp__lte=end_time,
            )
            if not re_process:
                location_filter &= Q(processed=False)

            # Fetch location data efficiently
            try:
                locations = Location.objects.filter(location_filter).order_by('timestamp').values(
                    'timestamp', 'point', 'device_id', 'action'
                )
                location_count = locations.count()
                logger.debug(
                    f"Found {location_count} location records to process")

            except DatabaseError as e:
                logger.error(
                    f"Database error while fetching locations: {str(e)}")
                raise

            # Convert to list format for processing
            location_list = []
            invalid_locations = 0

            for loc in locations:
                try:
                    if loc['point'] and loc['timestamp']:
                        location_list.append({
                            'timestamp': loc['timestamp'].isoformat(),
                            'point': {'coordinates': [loc['point'].x, loc['point'].y]},
                            'device_id': loc['device_id'],
                            'action': loc.get('action')
                        })
                    else:
                        invalid_locations += 1
                except (AttributeError, TypeError) as e:
                    logger.warning(f"Invalid location data: {str(e)}")
                    invalid_locations += 1

            if invalid_locations > 0:
                logger.warning(
                    f"Skipped {invalid_locations} invalid location records")

            if not location_list:
                logger.info("No valid location data found for processing")
                return []

            logger.debug(
                f"Processing {len(location_list)} valid location records")

            staypoints = LocationServices._process_location_clusters(
                location_list,
                spatial_threshold_meters,
                temporal_threshold_seconds,
                dwell_temporal_threshold_seconds,
                exit_defined_min_duration_seconds,
                max_interval_to_associate_exit_seconds
            )

            logger.info(
                f"Successfully identified {len(staypoints)} staypoints")
            return staypoints

        except Exception as e:
            logger.error(
                f"Error finding staypoints for profile {profile}: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def _process_location_clusters(
        location_list,
        spatial_threshold_meters,
        temporal_threshold_seconds,
        dwell_temporal_threshold_seconds,
        exit_defined_min_duration_seconds,
        max_interval_to_associate_exit_seconds
    ):
        """Process location clusters to identify staypoints."""
        staypoints = []
        i = 0
        n = len(location_list)
        min_points_in_cluster = 2

        while i < n:
            cluster_data = LocationServices._form_spatial_cluster(
                location_list, i, spatial_threshold_meters
            )

            if not cluster_data:
                i += 1
                continue

            departure_data = LocationServices._determine_departure_time(
                location_list, cluster_data, max_interval_to_associate_exit_seconds
            )

            staypoint = LocationServices._classify_staypoint(
                cluster_data, departure_data, location_list,
                temporal_threshold_seconds, dwell_temporal_threshold_seconds,
                exit_defined_min_duration_seconds, min_points_in_cluster
            )

            if staypoint:
                staypoints.append(staypoint)
                i = departure_data['next_index']
            else:
                i += 1

        return staypoints

    @staticmethod
    def _form_spatial_cluster(location_list, start_idx, spatial_threshold_meters):
        """Form a spatial cluster starting from the given index."""
        if start_idx >= len(location_list):
            return None

        arrival_location = location_list[start_idx]
        current_device_id = arrival_location['device_id']
        candidate_indices = [start_idx]
        cluster_end_index = start_idx

        j = start_idx + 1
        while j < len(location_list):
            next_point = location_list[j]

            # Check device ID consistency
            if next_point.get('device_id') != current_device_id:
                break

            # Calculate distance using haversine
            distance = haversine_distance(
                arrival_location['point']['coordinates'][1],
                arrival_location['point']['coordinates'][0],
                next_point['point']['coordinates'][1],
                next_point['point']['coordinates'][0]
            )

            if distance <= spatial_threshold_meters:
                # Don't include EXIT points in spatial cluster
                if next_point.get('action') == 'EXIT':
                    break
                candidate_indices.append(j)
                cluster_end_index = j
                j += 1
            else:
                break

        return {
            'arrival_location': arrival_location,
            'candidate_indices': candidate_indices,
            'cluster_end_index': cluster_end_index,
            'device_id': current_device_id
        }

    @staticmethod
    def _determine_departure_time(location_list, cluster_data, max_interval_seconds):
        """Determine actual departure time, considering EXIT actions."""
        cluster_end_index = cluster_data['cluster_end_index']
        device_id = cluster_data['device_id']

        # Default departure is last point in cluster
        default_departure = datetime.fromisoformat(
            location_list[cluster_end_index]['timestamp']
        )

        departure_data = {
            'departure_time': default_departure,
            'ended_by_exit': False,
            'next_index': cluster_end_index + 1
        }

        # Check for EXIT action immediately following cluster
        exit_idx = cluster_end_index + 1
        if exit_idx < len(location_list):
            potential_exit = location_list[exit_idx]

            if (potential_exit.get('action') == 'EXIT' and
                    potential_exit.get('device_id') == device_id):

                last_cluster_time = datetime.fromisoformat(
                    location_list[cluster_end_index]['timestamp']
                )
                exit_time = datetime.fromisoformat(potential_exit['timestamp'])
                time_diff = (exit_time - last_cluster_time).total_seconds()

                if 0 <= time_diff <= max_interval_seconds:
                    departure_data.update({
                        'departure_time': exit_time,
                        'ended_by_exit': True,
                        'next_index': exit_idx + 1
                    })

        return departure_data

    @staticmethod
    def _classify_staypoint(
        cluster_data, departure_data, location_list,
        temporal_threshold, dwell_threshold, exit_threshold, min_points
    ):
        """Classify whether the cluster qualifies as a staypoint."""
        arrival_time = datetime.fromisoformat(
            cluster_data['arrival_location']['timestamp']
        )
        departure_time = departure_data['departure_time']
        duration_seconds = (departure_time - arrival_time).total_seconds()

        if duration_seconds <= 0:
            return None

        candidate_indices = cluster_data['candidate_indices']
        ended_by_exit = departure_data['ended_by_exit']

        # Classification rules
        staypoint_type = None

        # Rule 1: EXIT-qualified staypoint
        if (ended_by_exit and
            duration_seconds >= exit_threshold and
                len(candidate_indices) >= 1):
            staypoint_type = 'exit_qualified'

        # Rule 2: Standard staypoint
        elif (duration_seconds >= temporal_threshold and
              len(candidate_indices) >= min_points):
            staypoint_type = 'standard'

        # Rule 3: DWELL-assisted staypoint
        elif (not ended_by_exit and
              duration_seconds >= dwell_threshold and
              len(candidate_indices) >= min_points):

            has_dwell = any(
                location_list[idx].get('action') == 'DWELL'
                for idx in candidate_indices
            )
            if has_dwell:
                staypoint_type = 'dwell_assisted'

        if not staypoint_type:
            return None

        # Calculate centroid
        avg_lat = sum(
            location_list[idx]['point']['coordinates'][1]
            for idx in candidate_indices
        ) / len(candidate_indices)

        avg_lon = sum(
            location_list[idx]['point']['coordinates'][0]
            for idx in candidate_indices
        ) / len(candidate_indices)

        return {
            'latitude': avg_lat,
            'longitude': avg_lon,
            'arrival_time': arrival_time,
            'departure_time': departure_time,
            'duration_seconds': duration_seconds,
            'device_id': cluster_data['device_id'],
            'is_dwell_assisted': staypoint_type == 'dwell_assisted',
            'is_exit_qualified': staypoint_type == 'exit_qualified',
            'stay_ended_by_exit_action': ended_by_exit,
            'staypoint_type': staypoint_type
        }

    @staticmethod
    def create_staypoints_from_data(
        profile,
        staypoints_data: list,  # List of dicts from find_staypoints...enhanced
        duplication_spatial_threshold_meters=50,  # Spatial radius for uniqueness check
        duplication_window_minutes=5  # Time window around start/end for uniqueness check
    ):
        """
        Creates StayPoint model instances from detected staypoint data,
        avoiding duplicates based on spatial and temporal overlap with existing records.

        Args:
            profile (Profile): The Django Profile instance.
            staypoints_data (list): List of staypoint dictionaries from a
                                     staypoint detection function (e.g., find_staypoints_for_user_in_range_enhanced).
            duplication_spatial_threshold_meters (float): Max distance (in meters) for a
                                                          potential new staypoint to be considered
                                                          a duplicate of an existing StayPoint.
            duplication_window_minutes (int): Time window (in minutes) around the
                                             new staypoint's start/end times to check
                                             for existing StayPoints' time overlap.

        Returns:
            list: A list of created StayPoint model instances.
        """
        try:
            logger.info(
                f"Creating staypoints from {len(staypoints_data) if staypoints_data else 0} data points for profile {profile}")

            if not staypoints_data:
                logger.info("No staypoint data provided, returning empty list")
                return []

            if not profile:
                logger.error("Profile parameter is required")
                raise ValueError("Profile parameter is required")

            created_staypoints = []
            duplication_distance_limit = D(
                m=duplication_spatial_threshold_meters)
            skipped_duplicates = 0
            processing_errors = 0

            # Process each detected staypoint
            for i, sp_data in enumerate(staypoints_data):
                try:
                    logger.debug(
                        f"Processing staypoint {i+1}/{len(staypoints_data)}")

                    # Validate required fields
                    required_fields = ['latitude', 'longitude',
                                       'arrival_time', 'departure_time']
                    for field in required_fields:
                        if field not in sp_data:
                            raise ValueError(
                                f"Missing required field: {field}")

                    sp_lat = sp_data['latitude']
                    sp_lon = sp_data['longitude']
                    sp_arrival = sp_data['arrival_time']
                    sp_departure = sp_data['departure_time']
                    sp_device_id = sp_data.get('device_id')

                    # Validate coordinate values
                    if not (-90 <= sp_lat <= 90):
                        raise ValueError(f"Invalid latitude: {sp_lat}")
                    if not (-180 <= sp_lon <= 180):
                        raise ValueError(f"Invalid longitude: {sp_lon}")

                    # Create a GeoDjango Point for the staypoint's centroid
                    try:
                        staypoint_geos_point = Point(sp_lon, sp_lat, srid=4326)
                    except GEOSException as e:
                        logger.error(
                            f"Failed to create Point for coordinates ({sp_lat}, {sp_lon}): {str(e)}")
                        processing_errors += 1
                        continue

                    # Define the time window for checking duplicates
                    duplication_check_start_time = sp_arrival - \
                        timedelta(minutes=duplication_window_minutes)
                    duplication_check_end_time = sp_departure + \
                        timedelta(minutes=duplication_window_minutes)

                    # Check for existing StayPoints that overlap spatially and temporally
                    try:
                        existing_staypoints = StayPoint.objects.filter(
                            profile=profile,
                            start_time__lte=duplication_check_end_time,
                            end_time__gte=duplication_check_start_time
                        ).annotate(
                            distance=Distance('point', staypoint_geos_point)
                        ).filter(
                            distance__lte=duplication_distance_limit
                        ).exists()
                    except DatabaseError as e:
                        logger.error(
                            f"Database error checking for duplicate staypoints: {str(e)}")
                        processing_errors += 1
                        continue

                    # Create the StayPoint instance if no duplicate is found
                    if not existing_staypoints:
                        try:
                            # Find the closest place for this staypoint
                            closest_place = None
                            try:
                                fallback_distance_limit = D(
                                    m=200)  # 200m fallback distance
                                closest_place = Place.objects.annotate(
                                    distance=Distance(
                                        'coordinates', staypoint_geos_point)
                                ).filter(
                                    browsable=True,
                                    approved=True
                                ).filter(
                                    Q(radius__isnull=False, radius__gt=0, distance__lte=F('radius')) |
                                    Q(Q(radius__isnull=True) | Q(radius=0),
                                      distance__lte=fallback_distance_limit)
                                ).order_by('distance').first()  # This already gets the closest place
                            except Exception as e:
                                logger.warning(
                                    f"Failed to find closest place for staypoint: {str(e)}")

                            new_staypoint = StayPoint(
                                profile=profile,
                                point=staypoint_geos_point,
                                start_time=sp_arrival,
                                end_time=sp_departure,
                                device_id=sp_device_id,
                                place=closest_place,
                            )
                            new_staypoint.save()
                            created_staypoints.append(new_staypoint)
                            place_info = f" at {closest_place.name}" if closest_place else ""
                            logger.debug(
                                f"Created StayPoint at ({sp_lat:.4f}, {sp_lon:.4f}) from {sp_arrival} to {sp_departure}{place_info}")
                        except (ValidationError, DatabaseError) as e:
                            logger.error(f"Failed to save StayPoint: {str(e)}")
                            processing_errors += 1
                    else:
                        skipped_duplicates += 1
                        logger.debug(
                            f"Skipped duplicate StayPoint at ({sp_lat:.4f}, {sp_lon:.4f})")

                except (KeyError, ValueError, TypeError) as e:
                    logger.error(
                        f"Invalid staypoint data at index {i}: {str(e)}")
                    processing_errors += 1
                    continue

            logger.info(
                f"Created {len(created_staypoints)} staypoints, skipped {skipped_duplicates} duplicates, {processing_errors} errors")
            return created_staypoints

        except Exception as e:
            logger.error(
                f"Error creating staypoints from data: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def update_staypoints_with_places(profile=None, limit=None):
        """
        Updates existing StayPoint records that don't have places associated with them
        by finding the closest suitable place for each staypoint.

        Args:
            profile: Optional profile to limit updates to. If None, updates all profiles.
            limit: Optional limit on number of staypoints to update.

        Returns:
            int: Number of staypoints updated with place associations.
        """
        try:
            logger.info(
                "Starting to update staypoints with place associations")

            # Build query for staypoints without places
            query = StayPoint.objects.filter(place__isnull=True)
            if profile:
                query = query.filter(profile=profile)

            staypoints = query.order_by('-created_at')
            if limit:
                staypoints = staypoints[:limit]

            updated_count = 0
            fallback_distance_limit = D(m=200)  # 200m fallback distance

            for staypoint in staypoints:
                try:
                    # Find the closest place for this staypoint
                    closest_place = Place.objects.annotate(
                        distance=Distance('coordinates', staypoint.point)
                    ).filter(
                        browsable=True,
                        approved=True
                    ).filter(
                        Q(radius__isnull=False, radius__gt=0, distance__lte=F('radius')) |
                        Q(Q(radius__isnull=True) | Q(radius=0),
                          distance__lte=fallback_distance_limit)
                    ).order_by('distance').first()  # This already gets the closest place

                    if closest_place:
                        staypoint.place = closest_place
                        staypoint.save(update_fields=['place'])
                        updated_count += 1
                        logger.debug(
                            f"Updated StayPoint {staypoint.id} with place {closest_place.name}")

                except Exception as e:
                    logger.error(
                        f"Failed to update staypoint {staypoint.id}: {str(e)}")
                    continue

            logger.info(
                f"Successfully updated {updated_count} staypoints with place associations")
            return updated_count

        except Exception as e:
            logger.error(
                f"Error updating staypoints with places: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def mark_locations_as_processed(profile, start_time: datetime, end_time: datetime):
        """
        Marks Location records for a specific profile within a date range as processed.

        Args:
            profile (Profile): The Django Profile instance.
            start_time (datetime): The start datetime of the processed range.
            end_time (datetime): The end datetime of the processed range.

        Returns:
            int: The number of Location records marked as processed.
        """
        try:
            logger.debug(
                f"Marking locations as processed for profile {profile} from {start_time} to {end_time}")

            # Validate parameters
            if not profile:
                logger.error("Profile parameter is required")
                raise ValueError("Profile parameter is required")

            if not start_time or not end_time:
                logger.error("Start time and end time are required")
                raise ValueError("Start time and end time are required")

            if start_time >= end_time:
                logger.error(
                    f"Start time ({start_time}) must be before end time ({end_time})")
                raise ValueError("Start time must be before end time")

            # Use a bulk update for efficiency
            updated_count = Location.objects.filter(
                profile=profile,
                timestamp__gte=start_time,
                timestamp__lte=end_time,
                processed=False  # Only update those not already marked
            ).update(processed=True)

            logger.debug(
                f"Successfully marked {updated_count} locations as processed")
            return updated_count

        except DatabaseError as e:
            logger.error(
                f"Database error marking locations as processed: {str(e)}")
            raise
        except Exception as e:
            logger.error(
                f"Error marking locations as processed: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def find_and_create_place_visits_from_staypoints(
        profile,
        staypoints_data: list,  # Each dict MUST include 'stay_ended_by_exit_action': bool
        max_distance_to_place_meters=200,
        # NEW: Defines how close a new staypoint's arrival must be to an existing
        # PlaceVisit's departure to be considered an extension of the same visit.
        merge_extension_threshold_minutes=30,
        notification_min_duration_seconds=300,  # 5 minutes
        # NEW: How recent a PlaceVisit's departure_time must be (relative to now)
        # to consider the user "still there" for notification purposes.
        # 4 minutes (allows for some processing lag)
        notification_departure_recency_seconds=240
    ):
        """
        Finds/creates PlaceVisit instances from staypoint data, intelligently
        updating/extending existing visits, and handles sending "at Place"
        notifications once per visit.

        Args:
            profile (Profile): The Django Profile instance.
            staypoints_data (list): List of staypoint dictionaries. Each dict MUST include
                                     'arrival_time', 'departure_time', 'latitude',
                                     'longitude', and 'stay_ended_by_exit_action'.
            max_distance_to_place_meters (float): Max distance to associate a staypoint with a Place.
            merge_extension_threshold_minutes (int): Time window (minutes) to consider a new
                                                     staypoint as an extension of an existing PlaceVisit.
            notification_min_duration_seconds (int): Min duration (seconds) at a place to trigger notification.
            notification_departure_recency_seconds (int): Max age (seconds) of a PlaceVisit's
                                                          departure_time to be considered "fresh" for notification.
        Returns:
            list: A list of upserted PlaceVisit model instances relevant to this run.
                  Each instance in this list has been saved to the database.
        """
        try:
            logger.info(
                f"Creating place visits from {len(staypoints_data) if staypoints_data else 0} staypoints for profile {profile}")

            if not staypoints_data:
                logger.info("No staypoint data provided, returning empty list")
                return []

            if not profile:
                logger.error("Profile parameter is required")
                raise ValueError("Profile parameter is required")

            upserted_visits_this_run = []
            # Consistent timestamp for this processing batch for notification logic
            now_for_notifications = tz.now()
            fallback_distance_limit = D(m=max_distance_to_place_meters)

            notifications_sent = 0
            visits_created = 0
            visits_updated = 0
            processing_errors = 0

            # 1. Process each staypoint from the input data
            for i, sp_data in enumerate(staypoints_data):
                try:
                    logger.debug(
                        f"Processing staypoint {i+1}/{len(staypoints_data)} for place visits")

                    # Validate required fields
                    required_fields = ['latitude', 'longitude',
                                       'arrival_time', 'departure_time']
                    for field in required_fields:
                        if field not in sp_data:
                            raise ValueError(
                                f"Missing required field: {field}")

                    sp_lat = sp_data['latitude']
                    sp_lon = sp_data['longitude']
                    sp_arrival = sp_data['arrival_time']
                    sp_departure = sp_data['departure_time']
                    sp_device_id = sp_data.get('device_id')

                    # Validate coordinates
                    if not (-90 <= sp_lat <= 90):
                        raise ValueError(f"Invalid latitude: {sp_lat}")
                    if not (-180 <= sp_lon <= 180):
                        raise ValueError(f"Invalid longitude: {sp_lon}")

                    # This flag is critical and must be provided by the staypoint generation logic
                    sp_stay_ended_by_exit = sp_data.get(
                        'stay_ended_by_exit_action', False)

                    # Create a GeoDjango Point for the staypoint's centroid
                    try:
                        staypoint_geos_point = Point(sp_lon, sp_lat, srid=4326)
                    except GEOSException as e:
                        logger.error(
                            f"Failed to create Point for coordinates ({sp_lat}, {sp_lon}): {str(e)}")
                        processing_errors += 1
                        continue

                    # 2. Find the closest suitable Place using GeoDjango, considering Place radius
                    closest_place = Place.objects.annotate(
                        distance=Distance('coordinates', staypoint_geos_point)
                    ).filter(
                        # Add any other conditions for a Place to be visitable, e.g., active, public
                        browsable=True,  # As per your original query
                        approved=True   # As per your original query
                    ).filter(
                        Q(radius__isnull=False, radius__gt=0, distance__lte=F('radius')) |
                        Q(Q(radius__isnull=True) | Q(radius=0),
                          distance__lte=fallback_distance_limit)
                    ).order_by('distance').first()

                    if not closest_place:
                        # No suitable place found for this staypoint, continue to the next staypoint
                        continue

                    # 3. Upsert Logic: Try to find and extend an existing PlaceVisit
                    # This will hold the PlaceVisit to be processed (either existing or new)
                    current_pv = None

                    # Query for a PlaceVisit that this staypoint might extend or that it overlaps with
                    # A visit is a candidate if it's for the same place/profile and:
                    #   a) Its departure_time is shortly before the new staypoint's arrival_time (extension)
                    #   b) Its time range significantly overlaps with the new staypoint's time range (reprocessing)
                    merge_candidate_pv = PlaceVisit.objects.filter(
                        profile=profile,
                        place=closest_place,
                    ).filter(
                        # Option A: Existing PV ends just before new SP starts (within threshold)
                        Q(departure_time__gte=sp_arrival - timedelta(minutes=merge_extension_threshold_minutes),
                          departure_time__lt=sp_arrival) |
                        # Option B: Existing PV's timeframe significantly overlaps with new SP's timeframe
                        Q(arrival_time__lte=sp_departure,
                          departure_time__gte=sp_arrival)
                    ).order_by('-departure_time').first()  # Get the latest one if multiple overlaps occur

                    if merge_candidate_pv:
                        # Found an existing visit to extend or update
                        current_pv = merge_candidate_pv

                        # Update times to encompass both the existing visit and the new staypoint data
                        current_pv.arrival_time = min(
                            current_pv.arrival_time, sp_arrival)
                        current_pv.departure_time = max(
                            current_pv.departure_time, sp_departure)

                        # Update other relevant fields from the latest staypoint data
                        current_pv.point = staypoint_geos_point
                        current_pv.recorded_distance = float(closest_place.distance.m) if hasattr(
                            closest_place.distance, 'm') else closest_place.distance
                        if sp_device_id:  # Only update if new device_id is provided
                            current_pv.device_id = sp_device_id
                        # current_pv.notification_sent_at is intentionally NOT reset here.
                        # If it was already sent for this continuous visit, it remains sent.
                    else:
                        # No suitable existing visit found, create a new PlaceVisit instance
                        current_pv = PlaceVisit(
                            profile=profile,
                            place=closest_place,
                            arrival_time=sp_arrival,
                            departure_time=sp_departure,
                            passive_record=True,
                            recorded_distance=float(closest_place.distance.m) if hasattr(
                                closest_place.distance, 'm') else closest_place.distance,
                            device_id=sp_device_id,
                            point=staypoint_geos_point,
                        )

                    # 4. Notification Logic (applied to current_pv, whether new or updated)
                    # Ensure current_pv.arrival_time and current_pv.departure_time are valid datetimes
                    if current_pv.arrival_time and current_pv.departure_time:
                        visit_duration_seconds = (
                            current_pv.departure_time - current_pv.arrival_time).total_seconds()

                        has_met_notification_duration = visit_duration_seconds >= notification_min_duration_seconds
                        not_yet_notified_for_this_visit = current_pv.notification_sent_at is None

                        # Determine if the user is likely still at the place for notification purposes
                        # Rule 1: The staypoint data that defined/updated this visit's end was NOT an exit event.
                        # This uses sp_stay_ended_by_exit from the *current* staypoint_data dict,
                        # as it's the most recent information about the end of this visit segment.
                        likely_ongoing_based_on_stay_end = not sp_stay_ended_by_exit

                        # Rule 2: The departure time of this PlaceVisit is very recent (user likely still there).
                        # FIXED: Calculate how long ago the departure time was (should be small/negative if user still there)
                        departure_age_seconds = (
                            now_for_notifications - current_pv.departure_time).total_seconds()
                        # If departure_age_seconds is negative, departure is in the future (user still there)
                        # If positive but small, departure was recent (user might still be there)
                        departure_is_fresh_enough_for_notification = departure_age_seconds <= notification_departure_recency_seconds

                        is_likely_still_there_for_notification = likely_ongoing_based_on_stay_end and departure_is_fresh_enough_for_notification

                        logger.debug(f"Notification check for {closest_place.name}: duration={visit_duration_seconds}s, "
                                     f"not_notified={not_yet_notified_for_this_visit}, ongoing={likely_ongoing_based_on_stay_end}, "
                                     f"departure_age={departure_age_seconds}s, fresh_enough={departure_is_fresh_enough_for_notification}")

                        if has_met_notification_duration and not_yet_notified_for_this_visit and is_likely_still_there_for_notification:
                            try:
                                # --- SEND ACTUAL NOTIFICATION HERE ---
                                logger.info(
                                    f"NOTIFICATION: User {profile.id} at Place {closest_place.name} for {visit_duration_seconds/60:.1f} mins. Visit ends {current_pv.departure_time}.")
                                NotificationsServices().send_notification(
                                    message=f"Looks like you're at {closest_place.name}. Check-in and let us know how it is!",
                                    title=f"You're at {closest_place.name}!",
                                    profile=profile,
                                    data={"place_id": closest_place.hash_id}
                                )
                                current_pv.notification_sent_at = now_for_notifications  # Mark as notified
                                notifications_sent += 1
                                logger.debug(
                                    f"Successfully sent notification for place visit to {closest_place.name}")
                            except Exception as e:
                                logger.error(
                                    f"Failed to send notification for place visit: {str(e)}", exc_info=True)
                                # Don't mark as notified if sending failed
                        elif not has_met_notification_duration:
                            logger.debug(
                                f"Visit duration {visit_duration_seconds}s below threshold {notification_min_duration_seconds}s")
                        elif not not_yet_notified_for_this_visit:
                            logger.debug(
                                f"Notification already sent at {current_pv.notification_sent_at}")
                        elif not is_likely_still_there_for_notification:
                            logger.debug(
                                f"User likely not still at location (exit={sp_stay_ended_by_exit}, age={departure_age_seconds}s)")

                    # 5. Save the PlaceVisit (either new or updated, possibly with notification_sent_at)
                    try:
                        # Track if this is a new visit or update
                        is_new_visit = current_pv.pk is None
                        current_pv.save()

                        if is_new_visit:
                            visits_created += 1
                            logger.debug(
                                f"Created new place visit for {closest_place.name}")
                        else:
                            visits_updated += 1
                            logger.debug(
                                f"Updated existing place visit for {closest_place.name}")

                        upserted_visits_this_run.append(current_pv)
                    except (ValidationError, DatabaseError) as e:
                        logger.error(f"Failed to save PlaceVisit: {str(e)}")
                        processing_errors += 1

                except (KeyError, ValueError, TypeError) as e:
                    logger.error(
                        f"Invalid staypoint data at index {i}: {str(e)}")
                    processing_errors += 1
                    continue
                except Exception as e:
                    logger.error(
                        f"Unexpected error processing staypoint {i}: {str(e)}", exc_info=True)
                    processing_errors += 1
                    continue

            logger.info(f"Place visit processing complete: {visits_created} created, {visits_updated} updated, "
                        f"{notifications_sent} notifications sent, {processing_errors} errors")
            return upserted_visits_this_run

        except Exception as e:
            logger.error(
                f"Error creating place visits from staypoints: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def process_location_data(
        profile_id: str = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        device_id: Optional[str] = None,
        only_stationary: bool = False,
        re_process: bool = False,
    ):
        """
        Main method to process location data for a profile, creating staypoints and place visits.

        Args:
            profile_id: Hash ID of the profile to process
            start_date: Start date for processing (defaults to 1 day ago)
            end_date: End date for processing (defaults to now)
            device_id: Optional device ID filter (currently unused)
            only_stationary: Optional flag to process only stationary data (currently unused)
            re_process: Whether to reprocess existing data

        Returns:
            tuple: (created_staypoints, place_visits)
        """
        try:
            logger.info(
                f"Starting location data processing for profile {profile_id}")

            # Validate required parameters
            if not profile_id:
                logger.error("Profile ID is required")
                raise ValueError("Profile ID is required")

            # Set default dates if not provided
            if not start_date:
                start_date = datetime.now(tz=timezone.utc) - timedelta(days=1)
                logger.debug(f"Using default start_date: {start_date}")

            if not end_date:
                end_date = datetime.now(tz=timezone.utc)
                logger.debug(f"Using default end_date: {end_date}")

            # Get profile
            try:
                profile = Profile.objects.get(hash_id=profile_id)
                logger.debug(f"Found profile: {profile}")
            except Profile.DoesNotExist:
                logger.error(f"Profile not found with hash_id: {profile_id}")
                raise ValueError(
                    f"Profile not found with hash_id: {profile_id}")
            except DatabaseError as e:
                logger.error(f"Database error retrieving profile: {str(e)}")
                raise

            # Step 1: Find staypoints
            logger.info("Step 1: Finding staypoints")
            staypoints = LocationServices.find_staypoints_for_user_in_range(
                profile=profile.id,
                start_time=start_date,
                end_time=end_date,
                temporal_threshold_seconds=300,
                spatial_threshold_meters=50,
                dwell_temporal_threshold_seconds=150,
                re_process=re_process
            )
            logger.info(f"Found {len(staypoints)} staypoints")

            # Step 2: Create staypoint records
            logger.info("Step 2: Creating staypoint records")
            created_staypoints = LocationServices.create_staypoints_from_data(
                profile=profile,
                staypoints_data=staypoints,
                duplication_spatial_threshold_meters=50,
                duplication_window_minutes=5
            )
            logger.info(f"Created {len(created_staypoints)} staypoint records")

            # Step 3: Create place visits
            logger.info("Step 3: Creating place visits")
            place_visits = LocationServices.find_and_create_place_visits_from_staypoints(
                profile=profile,
                staypoints_data=staypoints,
                max_distance_to_place_meters=100,
            )
            logger.info(f"Created/updated {len(place_visits)} place visits")

            # Step 4: Update any existing staypoints without places for this profile
            logger.info(
                "Step 4: Updating existing staypoints with place associations")
            updated_staypoints_count = LocationServices.update_staypoints_with_places(
                profile=profile, limit=100)  # Limit to avoid long processing times
            logger.info(
                f"Updated {updated_staypoints_count} existing staypoints with places")

            # Step 5: Mark locations as processed
            logger.info("Step 5: Marking locations as processed")
            processed_count = LocationServices.mark_locations_as_processed(
                profile, start_date, end_date)
            logger.info(f"Marked {processed_count} locations as processed")

            logger.info(
                f"Successfully completed location data processing for profile {profile_id}")
            return created_staypoints, place_visits

        except Exception as e:
            logger.error(
                f"Error processing location data for profile {profile_id}: {str(e)}", exc_info=True)
            raise
