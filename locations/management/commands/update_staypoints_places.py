from django.core.management.base import BaseCommand
from locations.services import LocationServices
from profiles.models import Profile


class Command(BaseCommand):
    help = 'Update existing staypoints with place associations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--profile-id',
            type=str,
            help='Hash ID of specific profile to update (optional)',
        )
        parser.add_argument(
            '--limit',
            type=int,
            help='Limit number of staypoints to update (optional)',
        )

    def handle(self, *args, **options):
        profile_id = options.get('profile_id')
        limit = options.get('limit')
        
        profile = None
        if profile_id:
            try:
                profile = Profile.objects.get(hash_id=profile_id)
                self.stdout.write(f"Updating staypoints for profile: {profile}")
            except Profile.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Profile with hash_id {profile_id} not found')
                )
                return
        else:
            self.stdout.write("Updating staypoints for all profiles")

        if limit:
            self.stdout.write(f"Limiting to {limit} staypoints")

        try:
            updated_count = LocationServices.update_staypoints_with_places(
                profile=profile, 
                limit=limit
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated {updated_count} staypoints with place associations'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error updating staypoints: {str(e)}')
            )
