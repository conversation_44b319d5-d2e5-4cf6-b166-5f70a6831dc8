import pandas as pd
import numpy as np
from sklearn.cluster import <PERSON><PERSON><PERSON>
from haversine import haversine, Unit
from collections import defaultdict

from django.core.management.base import BaseCommand
from django.db.models import F
from django.contrib.gis.geos import Point
from django.contrib.gis.db.models.functions import Distance
from datetime import timedelta
from django.utils import timezone
from django.db import transaction

# --- Adjust these import paths to match your project structure ---
from locations.models import Location, StayPoint, Place
from profiles.models import Profile


class Command(BaseCommand):
    help = 'Detects stay points using a hybrid place-first and clustering approach.'

    def add_arguments(self, parser):
        parser.add_argument('--eps-meters', type=int, default=50,
                            help='DBSCAN: The max distance between points to be considered neighbors.')
        parser.add_argument('--min-duration', type=int, default=10,
                            help='The minimum duration in minutes for a visit to be considered a stay point.')
        parser.add_argument('--min-samples', type=int, default=3,
                            help='DBSCAN: The minimum number of points to form a cluster.')
        parser.add_argument('--days', type=int, default=7,
                            help='The number of days of historical location data to fetch and process.')
        parser.add_argument('--reprocess', action='store_true',
                            help='Deletes recent staypoints and re-processes locations for the given day range.')
        parser.add_argument('--verbose', action='store_true',
                            help='Provides additional logging around the detection process.')

    def handle(self, *args, **options):
        days_to_process = options['days']
        self.verbose = options['verbose']

        if options['reprocess']:
            self._reprocess_data(days_to_process)

        self.stdout.write(self.style.SUCCESS(
            'Starting HYBRID stay point detection process...'))
        grace_period_hours = 2
        processing_cutoff = timezone.now() - timedelta(hours=grace_period_hours)
        time_window = timezone.now() - timedelta(days=days_to_process)

        locations_qs = Location.objects.filter(
            processed=False, timestamp__gte=time_window, point__isnull=False, timestamp__lt=processing_cutoff
        ).extra(select={'latitude': 'ST_Y(point::geometry)', 'longitude': 'ST_X(point::geometry)'}) \
         .order_by('profile_id', 'timestamp')

        if not locations_qs.exists():
            self.stdout.write(self.style.SUCCESS(
                'No new location points to process.'))
            return

        all_locations = list(locations_qs)
        self.stdout.write(
            f'Fetched {len(all_locations)} location points from the last {days_to_process} days.')

        # --- PASS 0: DWELL Action Clustering ---
        dwell_stay_points, processed_ids_pass0 = self.process_dwell_actions(
            all_locations, options)
        self.stdout.write(self.style.SUCCESS(
            f'PASS 0 (DWELL Clustering): Found {len(dwell_stay_points)} stay points from DWELL actions.'))

        # --- PASS 1: High-Confidence "Place-First" Session Processing ---
        remaining_after_dwell = [
            loc for loc in all_locations if loc.id not in processed_ids_pass0]
        place_stay_points, processed_ids_pass1 = self.process_place_sessions(
            remaining_after_dwell, options)
        self.stdout.write(self.style.SUCCESS(
            f'PASS 1 (Place-First): Found {len(place_stay_points)} stay points at known places.'))

        # --- PASS 2: Exploratory Clustering on Remaining Data ---
        all_processed_ids = processed_ids_pass0.union(processed_ids_pass1)
        remaining_locations = [
            loc for loc in all_locations if loc.id not in all_processed_ids]
        dbscan_stay_points = []
        if remaining_locations:
            self.stdout.write(
                f'PASS 2 (DBSCAN): Analyzing {len(remaining_locations)} remaining points...')
            dbscan_stay_points = self.detect_stay_points_dbscan(
                remaining_locations, options)
            self.stdout.write(self.style.SUCCESS(
                f'PASS 2 (DBSCAN): Found {len(dbscan_stay_points)} potential new stay points.'))

        all_stay_points = dwell_stay_points + place_stay_points + dbscan_stay_points
        if all_stay_points:
            self.create_staypoint_records(all_stay_points)
        else:
            self.stdout.write(self.style.WARNING(
                'No stay points were detected in this run.'))

        all_processed_ids = [loc.id for loc in all_locations]
        Location.objects.filter(
            id__in=all_processed_ids).update(processed=True)
        self.stdout.write(self.style.SUCCESS(
            f'\nSuccessfully marked {len(all_processed_ids)} location points as processed.'))

    def process_dwell_actions(self, all_locations, options):
        """
        Process DWELL actions using DBSCAN clustering to identify high-confidence staypoints.
        DWELL actions are strong indicators of meaningful stops.
        """
        stay_points = []
        processed_ids = set()

        # Filter for DWELL actions only
        dwell_locations = [
            loc for loc in all_locations if loc.action == 'DWELL']

        if not dwell_locations:
            return stay_points, processed_ids

        self.stdout.write(
            f'Found {len(dwell_locations)} DWELL actions to cluster.')

        # Group by profile for separate clustering
        locations_by_profile = defaultdict(list)
        for loc in dwell_locations:
            locations_by_profile[loc.profile_id].append(loc)

        for profile_id, user_dwell_locations in locations_by_profile.items():
            # Convert to DataFrame for processing
            dwell_data = [{
                'id': loc.id, 'device_id': loc.device_id, 'timestamp': loc.timestamp,
                'latitude': loc.latitude, 'longitude': loc.longitude,
                'accuracy': loc.accuracy, 'is_moving': loc.is_moving,
                'profile': loc.profile, 'action': loc.action
            } for loc in user_dwell_locations]

            dwell_df = pd.DataFrame(dwell_data)
            dwell_df['timestamp'] = pd.to_datetime(dwell_df['timestamp'])

            # Process DWELL actions with two approaches:
            # 1. Try clustering for multiple DWELL points
            # 2. Create individual staypoints for isolated DWELL actions
            dwell_clusters = self._process_dwell_actions_comprehensive(
                dwell_df, options)
            stay_points.extend(dwell_clusters)

            # Mark all DWELL locations as processed (whether clustered or not)
            processed_ids.update(loc.id for loc in user_dwell_locations)

        return stay_points, processed_ids

    def _process_dwell_actions_comprehensive(self, dwell_df, options):
        """
        Process DWELL actions comprehensively:
        1. Try clustering for multiple nearby DWELL points
        2. Create individual staypoints for isolated DWELL actions
        3. Be very lenient since DWELL already indicates meaningful stops
        """
        stay_points = []

        if len(dwell_df) == 0:
            return stay_points

        # First, try clustering if we have multiple points
        if len(dwell_df) >= 2:
            clustered_stay_points = self._cluster_dwell_locations(
                dwell_df, options)
            stay_points.extend(clustered_stay_points)

            # Track which points were successfully clustered
            clustered_point_ids = set()
            for sp in clustered_stay_points:
                # Find the original points that contributed to this cluster
                cluster_points = dwell_df[
                    (dwell_df['timestamp'] >= sp['arrival_time']) &
                    (dwell_df['timestamp'] <= sp['departure_time']) &
                    # Rough proximity check
                    (abs(dwell_df['latitude'] - sp['latitude']) < 0.001) &
                    (abs(dwell_df['longitude'] - sp['longitude']) < 0.001)
                ]
                clustered_point_ids.update(cluster_points['id'].tolist())
        else:
            clustered_point_ids = set()

        # Now process any remaining unclustered DWELL points
        # First, try to group them by proximity before creating individual staypoints
        unclustered_points = dwell_df[~dwell_df['id'].isin(
            clustered_point_ids)]

        if len(unclustered_points) > 0:
            individual_clusters = self._cluster_individual_dwell_points(
                unclustered_points)
            stay_points.extend(individual_clusters)

        return stay_points

    def _cluster_individual_dwell_points(self, unclustered_points):
        """
        Cluster individual DWELL points that weren't caught by the main clustering.
        This prevents creating multiple overlapping staypoints for closely spaced DWELL actions.
        """
        stay_points = []

        if len(unclustered_points) == 0:
            return stay_points

        # Convert to list for easier processing
        points_list = unclustered_points.to_dict('records')
        processed_indices = set()

        for i, current_point in enumerate(points_list):
            if i in processed_indices:
                continue

            # Start a new cluster with this point
            cluster_points = [current_point]
            processed_indices.add(i)

            # Find other nearby points to add to this cluster
            for j, other_point in enumerate(points_list):
                if j in processed_indices or i == j:
                    continue

                # Check spatial proximity (within 50m)
                distance = haversine(
                    (current_point['latitude'], current_point['longitude']),
                    (other_point['latitude'], other_point['longitude']),
                    unit=Unit.METERS
                )

                # Check temporal proximity (within 30 minutes)
                time_diff = abs((pd.to_datetime(current_point['timestamp']) -
                                 pd.to_datetime(other_point['timestamp'])).total_seconds())

                if distance <= 50 and time_diff <= 30 * 60:  # 50m and 30 minutes
                    cluster_points.append(other_point)
                    processed_indices.add(j)

            # Create a staypoint for this cluster
            if len(cluster_points) == 1:
                # Single DWELL point
                point = cluster_points[0]
                lat, lon = point['latitude'], point['longitude']
                arrival_time = pd.to_datetime(point['timestamp'])
                departure_time = arrival_time + \
                    pd.Timedelta(minutes=5)  # Minimum 5-minute stay
                source = 'INDIVIDUAL_DWELL'
            else:
                # Multiple DWELL points - use actual time range and weighted centroid
                timestamps = [pd.to_datetime(p['timestamp'])
                              for p in cluster_points]
                arrival_time = min(timestamps)
                # Add buffer to last point
                departure_time = max(timestamps) + pd.Timedelta(minutes=5)

                # Calculate weighted centroid
                total_weight = len(cluster_points)
                lat = sum(p['latitude'] for p in cluster_points) / total_weight
                lon = sum(p['longitude']
                          for p in cluster_points) / total_weight
                source = f'CLUSTERED_DWELL({len(cluster_points)})'

            # Find the closest place for this cluster
            closest_place = self._find_closest_place_for_point(
                lat, lon, buffer_meters=100)

            stay_points.append({
                'device_id': cluster_points[0]['device_id'],
                'profile': cluster_points[0]['profile'],
                'place': closest_place,
                'latitude': lat,
                'longitude': lon,
                'arrival_time': arrival_time,
                'departure_time': departure_time,
                'source': source
            })

        return stay_points

    def _cluster_dwell_locations(self, dwell_df, options):
        """
        Cluster DWELL locations using DBSCAN with parameters optimized for DWELL actions.
        First groups by day to prevent clustering across multiple days.
        """
        if len(dwell_df) < 2:
            return []

        stay_points = []

        # Group DWELL actions by temporal proximity to handle overnight stays
        # but prevent clustering across distant time periods
        temporal_groups = self._group_by_temporal_proximity(
            dwell_df, max_gap_hours=12)

        for group_df in temporal_groups:
            if len(group_df) < 2:
                continue

            coords = group_df[['latitude', 'longitude']].to_numpy()
            rad_coords = np.radians(coords)

            # Use larger epsilon for DWELL clustering since they indicate meaningful stops
            epsilon_rad = (options['eps_meters'] * 1.5) / \
                6371000  # 1.5x normal radius
            # More lenient min_samples
            min_samples = max(1, options['min_samples'] - 1)

            db = DBSCAN(eps=epsilon_rad, min_samples=min_samples,
                        algorithm='ball_tree', metric='haversine').fit(rad_coords)

            group_df = group_df.copy()
            group_df['cluster'] = db.labels_

            for cluster_id in set(db.labels_):
                if cluster_id != -1:  # Ignore noise points
                    cluster_points = group_df[group_df['cluster']
                                              == cluster_id]

                    # For DWELL actions, we're more lenient on duration since they indicate intent
                    duration = (cluster_points['timestamp'].max() -
                                cluster_points['timestamp'].min()).total_seconds() / 60

                    # Use shorter minimum duration for DWELL clusters
                    # Half the normal duration
                    min_duration = max(2, options['min_duration'] // 2)

                    if duration >= min_duration:
                        lat, lon = self.calculate_weighted_centroid(
                            cluster_points)

                        # Find the closest place for this DWELL cluster using database query
                        closest_place = self._find_closest_place_for_point(
                            lat, lon, buffer_meters=50)

                        stay_points.append({
                            'device_id': cluster_points['device_id'].iloc[0],
                            'profile': cluster_points.iloc[0]['profile'],
                            'place': closest_place,
                            'latitude': lat,
                            'longitude': lon,
                            'arrival_time': cluster_points['timestamp'].min(),
                            'departure_time': cluster_points['timestamp'].max(),
                            'source': 'DWELL_CLUSTER'
                        })

        return stay_points

    def _group_by_temporal_proximity(self, df, max_gap_hours=12):
        """
        Group DataFrame by temporal proximity to handle overnight stays
        while preventing clustering across distant time periods.
        """
        if len(df) == 0:
            return []

        # Sort by timestamp
        df_sorted = df.sort_values('timestamp').copy()
        groups = []
        current_group = [df_sorted.iloc[0]]

        for i in range(1, len(df_sorted)):
            current_row = df_sorted.iloc[i]
            last_row = current_group[-1]

            # Calculate time gap in hours
            time_gap = (current_row['timestamp'] -
                        last_row['timestamp']).total_seconds() / 3600

            if time_gap <= max_gap_hours:
                # Add to current group
                current_group.append(current_row)
            else:
                # Start new group
                if len(current_group) > 0:
                    groups.append(pd.DataFrame(current_group))
                current_group = [current_row]

        # Add the last group
        if len(current_group) > 0:
            groups.append(pd.DataFrame(current_group))

        return groups

    def _reprocess_data(self, days):
        self.stdout.write(self.style.WARNING(
            f"--- REPROCESS FLAG DETECTED ---"))
        time_window_reset = timezone.now() - timedelta(days=days)
        staypoints_to_delete = StayPoint.objects.filter(
            start_time__gte=time_window_reset)
        deleted_count, _ = staypoints_to_delete.delete()
        self.stdout.write(self.style.WARNING(
            f"Deleted {deleted_count} StayPoint records from the last {days} days."))
        locations_to_update = Location.objects.filter(
            timestamp__gte=time_window_reset)
        updated_count = locations_to_update.update(processed=False)
        self.stdout.write(self.style.WARNING(
            f"Marked {updated_count} Location records as unprocessed."))
        self.stdout.write(self.style.WARNING(
            f"--- Proceeding with detection ---"))

    def process_place_sessions(self, all_locations, options):
        stay_points, processed_ids = [], set()
        places = list(Place.objects.all())
        locations_by_profile = defaultdict(list)
        for loc in all_locations:
            locations_by_profile[loc.profile_id].append(loc)

        for profile_id, user_locations in locations_by_profile.items():
            active_session = None
            for loc in user_locations:
                matched_place = self._find_matching_place(loc, places)

                if active_session:
                    time_diff = (
                        loc.timestamp - active_session['last_timestamp']).total_seconds() / 60
                    if not matched_place or matched_place.id != active_session['place'].id or time_diff > 30:
                        self._finalize_session(
                            active_session, options, stay_points, processed_ids)
                        active_session = None

                if active_session is None and matched_place:
                    active_session = {'profile': loc.profile, 'device_id': loc.device_id, 'place': matched_place, 'points': [
                    ], 'start_time': loc.timestamp, 'last_timestamp': loc.timestamp}

                if active_session and matched_place and matched_place.id == active_session['place'].id:
                    active_session['points'].append(loc)
                    active_session['last_timestamp'] = loc.timestamp

            if active_session:
                self._finalize_session(
                    active_session, options, stay_points, processed_ids)
        return stay_points, processed_ids

    def _find_matching_place(self, location, places):
        if location.geofence and (location.action == 'DWELL' or location.action == 'ENTER'):
            try:
                place = Place.objects.get(hash_id=location.geofence)
            except Place.DoesNotExist:
                return None
            return place
        for place in places:
            if not place.coordinates:
                continue
            distance = haversine((location.latitude, location.longitude),
                                 (place.coordinates.y, place.coordinates.x), unit=Unit.METERS)
            if distance <= (place.radius or 100) + 20:
                return place
        return None

    def _finalize_session(self, session, options, stay_points, processed_ids):
        if not session['points']:
            return
        duration = (session['last_timestamp'] -
                    session['start_time']).total_seconds() / 60
        if duration >= options['min_duration']:
            dwell_points = [p for p in session['points']
                            if p.action == 'DWELL']
            points_for_centroid = dwell_points if dwell_points else session['points']

            lat, lon = self.calculate_weighted_centroid(pd.DataFrame(
                [{'latitude': p.latitude, 'longitude': p.longitude, 'accuracy': p.accuracy, 'is_moving': p.is_moving} for p in points_for_centroid]))

            if self.verbose:
                self.stdout.write(
                    f"  [Pass 1] Finalized session at Place '{session['place'].name}' ({duration:.1f} mins) for profile {session['profile'].id}")
            stay_points.append({'device_id': session['device_id'], 'profile': session['profile'], 'place': session['place'],
                               'latitude': lat, 'longitude': lon, 'arrival_time': session['start_time'], 'departure_time': session['last_timestamp']})
        processed_ids.update(p.id for p in session['points'])

    def detect_stay_points_dbscan(self, all_remaining_locations, options):
        stay_points = []
        locations_by_profile = defaultdict(list)
        for loc in all_remaining_locations:
            locations_by_profile[loc.profile.id].append(loc)

        for profile_id, locations in locations_by_profile.items():
            trips = []
            if not locations:
                continue
            current_trip = [locations[0]]
            for i in range(1, len(locations)):
                time_gap = (
                    locations[i].timestamp - locations[i-1].timestamp).total_seconds() / 3600
                if time_gap > 2.0:
                    trips.append(current_trip)
                    current_trip = []
                current_trip.append(locations[i])
            trips.append(current_trip)

            for trip in trips:
                if len(trip) < options['min_samples']:
                    continue
                trip_df = pd.DataFrame([{'latitude': loc.latitude, 'longitude': loc.longitude, 'timestamp': loc.timestamp,
                                       'accuracy': loc.accuracy, 'is_moving': loc.is_moving, 'device_id': loc.device_id, 'profile': loc.profile} for loc in trip])
                coords = trip_df[['latitude', 'longitude']].to_numpy()
                rad_coords = np.radians(coords)
                db = DBSCAN(eps=options['eps_meters']/6371000, min_samples=options['min_samples'],
                            algorithm='ball_tree', metric='haversine').fit(rad_coords)
                trip_df['cluster'] = db.labels_

                for cluster_id in set(db.labels_):
                    if cluster_id != -1:
                        cluster_points = trip_df[trip_df['cluster']
                                                 == cluster_id]
                        duration = (cluster_points['timestamp'].max(
                        ) - cluster_points['timestamp'].min()).total_seconds() / 60
                        if duration >= options['min_duration']:
                            lat, lon = self.calculate_weighted_centroid(
                                cluster_points)
                            if self.verbose:
                                self.stdout.write(
                                    f"  [Pass 2] Found cluster with {len(cluster_points)} points ({duration:.1f} mins) for device {cluster_points['device_id'].iloc[0]}")
                            stay_points.append({'device_id': cluster_points['device_id'].iloc[0], 'profile': cluster_points['profile'].iloc[0], 'place': None,
                                               'latitude': lat, 'longitude': lon, 'arrival_time': cluster_points['timestamp'].min(), 'departure_time': cluster_points['timestamp'].max()})
        return stay_points

    def calculate_weighted_centroid(self, points_df):
        if points_df.empty:
            return None, None
        if 'accuracy' not in points_df.columns or points_df['accuracy'].isnull().all():
            return points_df['latitude'].mean(), points_df['longitude'].mean()
        weights = 1.0 / (points_df['accuracy'].fillna(100.0) + 1e-6)
        if 'is_moving' in points_df.columns:
            weights *= points_df['is_moving'].apply(
                lambda x: 0.2 if x or pd.isna(x) else 1.0)
        lat = np.average(points_df['latitude'], weights=weights.values)
        lon = np.average(points_df['longitude'], weights=weights.values)
        return lat, lon

    def _find_closest_place_for_point(self, lat, lon, buffer_meters=50):
        """
        Efficiently find the closest place to a point using database spatial queries.
        Much faster than loading all places and doing distance calculations in Python.
        """
        from django.contrib.gis.geos import Point
        from django.contrib.gis.db.models.functions import Distance
        from django.db.models import Q, F
        from django.contrib.gis.measure import D

        point = Point(lon, lat, srid=4326)
        fallback_distance_limit = D(m=200)  # 200m fallback distance

        try:
            closest_place = Place.objects.annotate(
                distance=Distance('coordinates', point)
            ).filter(
                browsable=True,
                approved=True
            ).filter(
                Q(radius__isnull=False, radius__gt=0, distance__lte=F('radius') + buffer_meters) |
                Q(Q(radius__isnull=True) | Q(radius=0),
                  distance__lte=fallback_distance_limit)
            ).order_by('distance').first()

            return closest_place
        except Exception as e:
            # Fallback to None if there's any issue with the spatial query
            return None

    def _deduplicate_staypoints(self, all_stay_points):
        """
        Deduplicate overlapping staypoints by merging those that are spatially and temporally close.
        This prevents creating multiple staypoints for the same actual stay.
        """
        if not all_stay_points:
            return []

        # Group by profile and device for separate processing
        staypoints_by_profile = defaultdict(list)
        for sp in all_stay_points:
            key = (sp['profile'].id if hasattr(sp['profile'], 'id')
                   else sp['profile'], sp.get('device_id', 'unknown'))
            staypoints_by_profile[key].append(sp)

        deduplicated = []

        for (profile_id, device_id), staypoints in staypoints_by_profile.items():
            # Sort by arrival time
            staypoints.sort(key=lambda x: x['arrival_time'])

            merged_staypoints = []

            for current_sp in staypoints:
                merged = False

                # Try to merge with existing staypoints
                for i, existing_sp in enumerate(merged_staypoints):
                    if self._should_merge_staypoints(existing_sp, current_sp):
                        # Log the merge decision
                        self._log_merge_decision(existing_sp, current_sp)

                        # Merge the staypoints
                        merged_sp = self._merge_staypoints(
                            existing_sp, current_sp)
                        merged_staypoints[i] = merged_sp
                        merged = True
                        break

                if not merged:
                    merged_staypoints.append(current_sp)

            deduplicated.extend(merged_staypoints)

        return deduplicated

    def _log_merge_decision(self, existing_sp, current_sp):
        """
        Log detailed information about why two staypoints are being merged.
        """
        # Calculate spatial distance
        distance = haversine(
            (existing_sp['latitude'], existing_sp['longitude']),
            (current_sp['latitude'], current_sp['longitude']),
            unit=Unit.METERS
        )

        # Calculate temporal relationship
        existing_start = existing_sp['arrival_time']
        existing_end = existing_sp['departure_time']
        current_start = current_sp['arrival_time']
        current_end = current_sp['departure_time']

        # Check for overlap
        overlaps = (existing_start <= current_end) and (
            current_start <= existing_end)

        # Calculate time gap if no overlap
        time_gap = None
        if not overlaps:
            time_gap = min(
                abs((existing_start - current_end).total_seconds()),
                abs((current_start - existing_end).total_seconds())
            ) / 60  # Convert to minutes

        # Format place names
        existing_place = existing_sp.get('place')
        current_place = current_sp.get('place')
        existing_place_name = existing_place.name if existing_place else "Unknown"
        current_place_name = current_place.name if current_place else "Unknown"

        # Format time ranges
        existing_time = f"{existing_start.strftime('%H:%M')}-{existing_end.strftime('%H:%M')}"
        current_time = f"{current_start.strftime('%H:%M')}-{current_end.strftime('%H:%M')}"

        # Log the merge decision
        if overlaps:
            self.stdout.write(self.style.WARNING(
                f"  [MERGE] Overlapping staypoints:"
            ))
        else:
            self.stdout.write(self.style.WARNING(
                f"  [MERGE] Close staypoints ({time_gap:.1f}min gap):"
            ))

        self.stdout.write(
            f"    Existing: {existing_place_name} ({existing_time}) [{existing_sp.get('source', 'UNKNOWN')}]"
        )
        self.stdout.write(
            f"    Current:  {current_place_name} ({current_time}) [{current_sp.get('source', 'UNKNOWN')}]"
        )
        self.stdout.write(
            f"    Distance: {distance:.1f}m"
        )

        # Show the merged result
        merged_start = min(existing_start, current_start)
        merged_end = max(existing_end, current_end)
        merged_duration = (merged_end - merged_start).total_seconds() / 60
        merged_time = f"{merged_start.strftime('%H:%M')}-{merged_end.strftime('%H:%M')}"

        # Determine which place will be kept
        existing_duration = (existing_end - existing_start).total_seconds()
        current_duration = (current_end - current_start).total_seconds()
        primary_place = existing_place_name if existing_duration >= current_duration else current_place_name

        self.stdout.write(
            f"    Result:   {primary_place} ({merged_time}) [{merged_duration:.1f}min total]"
        )

    def _should_merge_staypoints(self, sp1, sp2):
        """
        Determine if two staypoints should be merged based on spatial and temporal overlap.
        Only merge if they actually overlap in time AND space, not just proximity.
        """
        # Check spatial proximity (within 100m)
        distance = haversine(
            (sp1['latitude'], sp1['longitude']),
            (sp2['latitude'], sp2['longitude']),
            unit=Unit.METERS
        )

        if distance > 100:  # Too far apart spatially
            return False

        # Check for actual temporal overlap (not just proximity)
        # Two time ranges overlap if: start1 <= end2 AND start2 <= end1
        sp1_start = sp1['arrival_time']
        sp1_end = sp1['departure_time']
        sp2_start = sp2['arrival_time']
        sp2_end = sp2['departure_time']

        # Check for actual overlap or very close proximity (within 10 minutes)
        overlaps = (sp1_start <= sp2_end) and (sp2_start <= sp1_end)

        if overlaps:
            return True

        # Also merge if they're very close in time (within 10 minutes gap)
        time_gap = min(
            abs((sp1_start - sp2_end).total_seconds()),
            abs((sp2_start - sp1_end).total_seconds())
        )

        return time_gap <= 10 * 60  # 10 minutes gap (much more conservative)

    def _merge_staypoints(self, sp1, sp2):
        """
        Merge two overlapping staypoints into one, taking the broader time range and best location.
        """
        # Use the broader time range
        merged_arrival = min(sp1['arrival_time'], sp2['arrival_time'])
        merged_departure = max(sp1['departure_time'], sp2['departure_time'])

        # Prefer the place from the longer duration staypoint, or the first one if equal
        sp1_duration = (sp1['departure_time'] -
                        sp1['arrival_time']).total_seconds()
        sp2_duration = (sp2['departure_time'] -
                        sp2['arrival_time']).total_seconds()

        primary_sp = sp1 if sp1_duration >= sp2_duration else sp2

        # Use weighted average for coordinates based on duration
        total_duration = sp1_duration + sp2_duration
        if total_duration > 0:
            weight1 = sp1_duration / total_duration
            weight2 = sp2_duration / total_duration

            merged_lat = sp1['latitude'] * weight1 + sp2['latitude'] * weight2
            merged_lon = sp1['longitude'] * \
                weight1 + sp2['longitude'] * weight2
        else:
            merged_lat = (sp1['latitude'] + sp2['latitude']) / 2
            merged_lon = (sp1['longitude'] + sp2['longitude']) / 2

        return {
            'device_id': primary_sp['device_id'],
            'profile': primary_sp['profile'],
            # Prefer non-None place
            'place': primary_sp.get('place') or sp1.get('place') or sp2.get('place'),
            'latitude': merged_lat,
            'longitude': merged_lon,
            'arrival_time': merged_arrival,
            'departure_time': merged_departure,
            'source': f"MERGED({sp1.get('source', 'UNKNOWN')},{sp2.get('source', 'UNKNOWN')})"
        }

    def create_staypoint_records(self, all_stay_points):
        self.stdout.write("Deduplicating and creating StayPoint records...")

        # First, deduplicate overlapping staypoints
        deduplicated_stay_points = self._deduplicate_staypoints(
            all_stay_points)
        self.stdout.write(
            f"Deduplicated {len(all_stay_points)} -> {len(deduplicated_stay_points)} staypoints")

        stay_points_to_create = []
        places = list(Place.objects.all())

        for sp_data in deduplicated_stay_points:
            profile = sp_data['profile']
            if not profile or pd.isna(sp_data['longitude']) or pd.isna(sp_data['latitude']):
                continue

            calculated_center = Point(
                sp_data['longitude'], sp_data['latitude'], srid=4326)
            nearest_place = sp_data.get('place')
            if not nearest_place:
                closest_place = None
                min_distance = float('inf')

                for place in places:
                    if not place.coordinates:
                        continue
                    distance = haversine((calculated_center.y, calculated_center.x), (
                        place.coordinates.y, place.coordinates.x), unit=Unit.METERS)
                    if distance < 100 and distance < min_distance:
                        closest_place = place
                        min_distance = distance

                if closest_place:
                    nearest_place = closest_place
                    if self.verbose:
                        self.stdout.write(
                            f"  [Snap] Snapping DBSCAN cluster to closest Place: {nearest_place.name} ({min_distance:.1f}m away)")

            final_point = nearest_place.coordinates if nearest_place else calculated_center

            if nearest_place:
                historical_staypoints = StayPoint.objects.filter(
                    profile=profile, place=nearest_place)
                current_duration = (
                    sp_data['departure_time'] - sp_data['arrival_time']).total_seconds()
                total_weighted_lat, total_weighted_lon, total_duration = (
                    calculated_center.y * current_duration, calculated_center.x * current_duration, current_duration)
                for sp in historical_staypoints:
                    hist_duration = (
                        sp.end_time - sp.start_time).total_seconds()
                    if hist_duration > 0:
                        total_weighted_lat += sp.point.y * hist_duration
                        total_weighted_lon += sp.point.x * hist_duration
                        total_duration += hist_duration
                if total_duration > 0:
                    final_point = Point(
                        total_weighted_lon / total_duration, total_weighted_lat / total_duration, srid=4326)
                    if self.verbose:
                        self.stdout.write(self.style.HTTP_INFO(
                            f"  Stabilized point for '{nearest_place.name}' using {historical_staypoints.count()} historical visits."))

            stay_points_to_create.append(StayPoint(
                profile=profile, device_id=sp_data['device_id'], point=final_point, start_time=sp_data['arrival_time'], end_time=sp_data['departure_time'], place=nearest_place))

        if stay_points_to_create:
            with transaction.atomic():
                StayPoint.objects.bulk_create(stay_points_to_create)
            self.stdout.write(self.style.SUCCESS(
                f'Successfully created {len(stay_points_to_create)} new StayPoint records.'))
