from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, F
from datetime import timedelta
from django.contrib.gis.geos import Point
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.measure import D

# --- Adjust these import paths to match your project structure ---
from locations.models import StayPoint
from profiles.models import Profile


class Command(BaseCommand):
    help = 'Predicts the next likely staypoint for a user based on historical data and current location.'

    def add_arguments(self, parser):
        parser.add_argument('--profile', type=str, required=True,
                            help='The hash_id of the profile to predict for.')
        parser.add_argument('--lat', type=float, default=40.7831,
                            help='The user\'s current latitude. Defaults to Manhattan.')
        parser.add_argument('--lng', type=float, default=-73.9712,
                            help='The user\'s current longitude. Defaults to Manhattan.')
        parser.add_argument('--radius-km', type=int, default=10,
                            help='Search radius in kilometers from current location.')
        parser.add_argument('--hours-ahead', type=int, default=0,
                            help='Optional: Number of hours to look ahead for the prediction. Defaults to 0 (now).')

    def handle(self, *args, **options):
        profile_hash_id = options['profile']
        current_lat = options['lat']
        current_lng = options['lng']
        radius_km = options['radius_km']
        hours_ahead = options['hours_ahead']

        try:
            profile = Profile.objects.get(hash_id=profile_hash_id)
        except Profile.DoesNotExist:
            self.stdout.write(self.style.ERROR(
                f"Profile with hash_id {profile_hash_id} not found."))
            return

        # Determine the time context for the prediction (now or in the future)
        now = timezone.now()
        prediction_time = now + timedelta(hours=hours_ahead)

        if hours_ahead > 0:
            self.stdout.write(
                f"Analyzing staypoint history for {profile.user.username}, predicting for {hours_ahead} hours from now.")
        else:
            self.stdout.write(
                f"Analyzing staypoint history for {profile.user.username} for the current time.")

        base_staypoints = StayPoint.objects.filter(
            profile=profile, place__isnull=False)

        # --- Rule 0: Proximity Filter (If location is provided) ---
        if current_lat is not None and current_lng is not None:
            self.stdout.write(
                f"Filtering recommendations within {radius_km}km of current location ({current_lat}, {current_lng}).")
            current_location = Point(current_lng, current_lat, srid=4326)

            # Find all unique places the user has visited within the radius
            nearby_places = base_staypoints.annotate(
                distance_from_current=Distance('point', current_location)
            ).filter(distance_from_current__lte=D(km=radius_km)).values_list('place__id', flat=True).distinct()

            if not nearby_places:
                self.stdout.write(self.style.WARNING(
                    f"No historical staypoints found within {radius_km}km. Analyzing all history."))
            else:
                base_staypoints = base_staypoints.filter(
                    place__id__in=nearby_places)

        # --- Rule 1: Strong Time-Based Routine Check ---
        day_of_week = prediction_time.weekday() + 1  # Monday is 1 and Sunday is 7
        time_slot = self.get_time_slot(prediction_time.hour)

        self.stdout.write(
            f"Prediction Context: Day={prediction_time.strftime('%A')}, Time Slot='{time_slot}'")

        routine_staypoints = base_staypoints.filter(
            start_time__week_day=day_of_week
        ).annotate(
            hour=F('start_time__hour')
        ).filter(
            hour__in=self.get_time_slot_hours(time_slot)
        ).values(
            'place__name'
        ).annotate(
            visit_count=Count('place__id')
        ).order_by('-visit_count')

        if routine_staypoints and routine_staypoints[0]['visit_count'] >= 2:
            prediction = routine_staypoints[0]
            self.stdout.write(self.style.SUCCESS(
                f"\nPrediction (High Confidence): Found a strong routine."))
            self.stdout.write(
                f"  -> Likely destination: {prediction['place__name']}")
            self.stdout.write(
                f"  -> Reason: User has visited this place {prediction['visit_count']} times on a {prediction_time.strftime('%A')} {time_slot.lower()}.")
            return

        # --- Rule 2: General Frequency/Recency Check (Fallback) ---
        self.stdout.write(self.style.WARNING(
            "\nNo strong time-based routine found. Falling back to general frequency."))

        recent_window = now - timedelta(days=90)
        general_staypoints = base_staypoints.filter(
            start_time__gte=recent_window
        ).values(
            'place__name'
        ).annotate(
            visit_count=Count('place__id')
        ).order_by('-visit_count')

        if general_staypoints:
            prediction = general_staypoints[0]
            self.stdout.write(self.style.SUCCESS(
                f"\nPrediction (Medium Confidence): Based on general habits."))
            self.stdout.write(
                f"  -> Likely destination: {prediction['place__name']}")
            self.stdout.write(
                f"  -> Reason: This is the most visited place ({prediction['visit_count']} times) in the last 90 days (within the search area).")
        else:
            self.stdout.write(self.style.ERROR(
                "\nNo prediction possible: Insufficient staypoint history for this user."))

    def get_time_slot(self, hour):
        if 5 <= hour < 12:
            return 'Morning'
        elif 12 <= hour < 17:
            return 'Afternoon'
        elif 17 <= hour < 22:
            return 'Evening'
        else:
            return 'Night'

    def get_time_slot_hours(self, slot_name):
        if slot_name == 'Morning':
            return range(5, 12)
        elif slot_name == 'Afternoon':
            return range(12, 17)
        elif slot_name == 'Evening':
            return range(17, 22)
        else:
            return list(range(22, 24)) + list(range(0, 5))
