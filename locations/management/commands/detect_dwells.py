import numpy as np
from datetime import timedelta
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.contrib.gis.geos import Point
from django.contrib.gis.db.models.functions import Distance
from django.db.models import F

# --- Adjust these import paths to match your project structure ---
from locations.models import Location, Place
from profiles.models import Profile
# from your_notifications_app.services import send_silent_dwell_check_notification


# --- Placeholder for your actual notification service ---
def send_silent_dwell_check_notification(profile, dwell_center, place):
    """
    This function is a placeholder. In a real implementation, this would
    trigger a silent push notification to the user's device.
    """
    print(
        f"--> SILENT PUSH: Sending to device for profile {profile.id} at {place.name} ({dwell_center.wkt})")
    # Example:
    # send_push(
    #     device_token=profile.device_token,
    #     payload={"action": "CONFIRM_DWELL", "lat": dwell_center.y, "lon": dwell_center.x, "place_id": place.id},
    #     content_available=True  # This makes it a silent push on iOS
    # )
    pass


class Command(BaseCommand):
    help = 'Detects potential user dwells at known Places in real-time and triggers a check.'

    def handle(self, *args, **options):
        # --- Configurable Parameters ---
        ACTIVE_WINDOW_MINUTES = 30
        DWELL_CHECK_MINUTES = 15
        MIN_DWELL_DURATION_MINUTES = 10
        MAX_STD_DEV_DEGREES = 0.0005  # Approx 55 meters
        MIN_POINTS_FOR_DWELL = 3
        NOTIFICATION_COOLDOWN_MINUTES = 60
        NOTIFICATION_MIN_DISTANCE_METERS = 200

        # --- 1. Identify Recently Active Users ---
        active_cutoff = timezone.now() - timedelta(minutes=ACTIVE_WINDOW_MINUTES)

        # --- FIXED QUERY ---
        # Added .order_by('device_id', '-timestamp') before .distinct() to satisfy PostgreSQL's requirement
        # that the ORDER BY clause must match the DISTINCT ON fields.
        recent_locations = Location.objects.filter(
            timestamp__gte=active_cutoff
        ).order_by('device_id', '-timestamp').distinct('device_id')

        active_device_ids = [loc.device_id for loc in recent_locations]

        if not active_device_ids:
            self.stdout.write(self.style.SUCCESS(
                'No recently active devices to check.'))
            return

        self.stdout.write(
            f"Checking for dwells for {len(active_device_ids)} active device(s)...")

        # --- 2. For Each Active User, Check for a Potential Dwell ---
        dwell_check_cutoff = timezone.now() - timedelta(minutes=DWELL_CHECK_MINUTES)

        profiles_to_update = []

        for device_id in active_device_ids:
            recent_points_qs = Location.objects.filter(
                device_id=device_id,
                timestamp__gte=dwell_check_cutoff
            ).extra(select={'latitude': 'ST_Y(point::geometry)', 'longitude': 'ST_X(point::geometry)'}) \
             .order_by('timestamp')

            if len(recent_points_qs) < MIN_POINTS_FOR_DWELL:
                continue

            first_point_time = recent_points_qs.first().timestamp
            last_point_time = recent_points_qs.last().timestamp
            duration = (last_point_time -
                        first_point_time).total_seconds() / 60

            if duration < MIN_DWELL_DURATION_MINUTES:
                continue

            coords = np.array([[p.latitude, p.longitude]
                              for p in recent_points_qs])
            std_dev = np.std(coords, axis=0)

            if std_dev[0] > MAX_STD_DEV_DEGREES or std_dev[1] > MAX_STD_DEV_DEGREES:
                continue

            # --- Dwell Detected! Now verify it's at a known Place. ---
            center_lon, center_lat = np.mean(
                coords[:, 1]), np.mean(coords[:, 0])
            dwell_center = Point(center_lon, center_lat, srid=4326)

            # Check if this dwell center is near a known Place. If not, we ignore it.
            nearest_place = Place.objects.annotate(
                distance=Distance('coordinates', dwell_center)
            ).filter(
                distance__lte=F('radius') + 50  # Dynamic radius + 50m buffer
            ).order_by('distance').first()

            if not nearest_place:
                # Dwell is not at a known place, so we don't care about it.
                continue

            # --- Now proceed with notification rules for the matched place ---
            profile = Profile.objects.filter(
                locations__device_id=device_id).first()
            if not profile:
                continue

            if profile.last_notification_timestamp:
                cooldown_cutoff = timezone.now() - timedelta(minutes=NOTIFICATION_COOLDOWN_MINUTES)
                if profile.last_notification_timestamp > cooldown_cutoff:
                    continue

            if profile.last_notification_point:
                # Use the place's location for distance check to avoid re-notifying for the same place
                distance = nearest_place.coordinates.distance(
                    profile.last_notification_point) * 100000
                if distance < NOTIFICATION_MIN_DISTANCE_METERS:
                    continue

            # --- All checks passed. Trigger the notification workflow. ---
            self.stdout.write(self.style.SUCCESS(
                f"Potential dwell detected for profile {profile.id} at Place: {nearest_place.name}"))

            send_silent_dwell_check_notification(
                profile, dwell_center, nearest_place)

            # Update profile with the location of the PLACE to prevent re-notifying at the same location
            profile.last_notification_point = nearest_place.coordinates
            profile.last_notification_timestamp = timezone.now()
            profiles_to_update.append(profile)

        # Bulk update all profiles that received a notification
        if profiles_to_update:
            Profile.objects.bulk_update(
                profiles_to_update, ['last_notification_point', 'last_notification_timestamp'])
            self.stdout.write(
                f"Updated {len(profiles_to_update)} profiles with notification timestamps.")
