from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone

from locations.services import LocationServices


class Command(BaseCommand):
    help = 'Process staypoints for location data within specified time range'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reprocess',
            action='store_true',
            help='Reprocess existing staypoints'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to look back for processing. Defaults to 7.'
        )

        # Also get the profile ID
        parser.add_argument(
            '--profile',
            type=str,
            help='Profile ID to process. If not provided, processes all profiles.'
        )

    def handle(self, *args, **options):
        reprocess = options['reprocess']
        days = options['days']
        profile_id = options['profile']

        start_date = timezone.now() - timedelta(days=days)

        self.stdout.write(self.style.SUCCESS(
            f'Processing staypoints from {start_date} (last {days} days)...'))

        result = LocationServices.process_location_data(
            start_date=start_date,
            re_process=reprocess,
            profile_id=profile_id
        )

        self.stdout.write(self.style.SUCCESS(
            f'Successfully processed staypoints.'))
        self.stdout.write(self.style.NOTICE(
            f'Created {len(result[0])} staypoints and {len(result[1])} place visits.'))
