from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction

# --- Adjust these import paths to match your project structure ---
from locations.models import Location, StayPoint


class Command(BaseCommand):
    help = 'Resets recent data for testing by deleting recent StayPoints and marking recent Locations as unprocessed.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Specifies the number of days of data to reset. Defaults to 7.'
        )
        parser.add_argument(
            '--no-input',
            action='store_true',
            help='Skips the confirmation prompt. Use with caution.'
        )

    def handle(self, *args, **options):
        days_to_reset = options['days']
        no_input = options['no_input']

        time_window = timezone.now() - timedelta(days=days_to_reset)

        # Find the objects to be deleted/updated
        staypoints_to_delete = StayPoint.objects.filter(
            start_time__gte=time_window)
        locations_to_update = Location.objects.filter(
            timestamp__gte=time_window, processed=True)

        if not staypoints_to_delete.exists() and not locations_to_update.exists():
            self.stdout.write(self.style.SUCCESS(
                f"No staypoints or processed locations found within the last {days_to_reset} days to reset."))
            return

        self.stdout.write(self.style.WARNING(
            f"This command will perform the following actions for the last {days_to_reset} days:"))
        self.stdout.write(
            f"  - Delete {staypoints_to_delete.count()} StayPoint records.")
        self.stdout.write(
            f"  - Mark {locations_to_update.count()} Location records as unprocessed.")

        if not no_input:
            confirmation = input(
                "Are you sure you want to continue? (yes/no): ")
            if confirmation.lower() != 'yes' and confirmation.lower() != 'y':
                self.stdout.write(self.style.ERROR("Operation cancelled."))
                return

        try:
            with transaction.atomic():
                # (1) Delete StayPoints
                deleted_count, _ = staypoints_to_delete.delete()

                # (2) Mark Locations as unprocessed
                updated_count = locations_to_update.update(processed=False)

            self.stdout.write(self.style.SUCCESS(
                f"\nSuccessfully deleted {deleted_count} StayPoint records."))
            self.stdout.write(self.style.SUCCESS(
                f"Successfully marked {updated_count} Location records as unprocessed."))
            self.stdout.write(self.style.SUCCESS("Reset complete."))

        except Exception as e:
            self.stdout.write(self.style.ERROR(
                f"An error occurred during the reset operation: {e}"))
