{% extends 'base.html' %}

{% block title %}Staypoint Map - GTX{% endblock %}

{% block extra_css %}
    <!-- Leaflet.js CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" xintegrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- flatpickr for date range picker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />

    <style>
      /* Override base template styles for full-screen map */
      body {
        display: flex;
        flex-direction: column;
        height: 100vh;
        margin: 0;
        overflow: hidden;
      }

      .navbar-custom {
        height: auto;
      }

      .main-content {
        padding: 0 !important;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0; /* Important for flex child to shrink */
      }

      .container-fluid {
        padding: 0 !important;
        max-width: none !important;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
      }

      #app-container {
        display: flex;
        flex: 1;
        min-height: 0;
        width: 100%;
      }
      #map-container {
        flex-grow: 1;
        height: 100%;
        position: relative;
      }
      #map {
        height: 100%;
        width: 100%;
      }
      #side-panel {
        width: 300px;
        min-width: 200px;
        height: 100%;
        background-color: #f8f9fa;
        border-left: 1px solid #dee2e6;
        display: flex;
        flex-direction: column;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease-in-out;
      }
      #controls-container {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
      }
      .date-picker-wrapper {
        position: relative;
        margin-bottom: 15px;
      }
      .date-picker-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
      }
      /* This targets the visible input created by flatpickr */
      .date-picker-wrapper .flatpickr-input {
        border: 1px solid #ced4da;
        background-color: #fff;
        padding: 10px 10px 10px 35px; /* Left padding for icon */
        width: 100%;
        box-sizing: border-box;
        text-align: center;
        border-radius: 5px;
        font-size: 0.95em;
        cursor: pointer;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      }
      .date-picker-wrapper .flatpickr-input:hover {
        border-color: #86b7fe;
      }
      .toggle-switch {
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: opacity 0.3s ease, max-height 0.3s ease, margin 0.3s ease, padding 0.3s ease;
        overflow: hidden;
        max-height: 50px;
        padding: 10px 0;
      }

      .toggle-switch.hidden {
        opacity: 0;
        max-height: 0;
        margin-bottom: 0;
        padding: 0;
      }
      .toggle-switch label {
        color: #495057;
      }
      .toggle-switch .switch {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 22px;
      }
      .toggle-switch .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
        border-radius: 22px;
      }
      .slider:before {
        position: absolute;
        content: '';
        height: 16px;
        width: 16px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }
      input:checked + .slider {
        background-color: #0d6efd;
      }
      input:checked + .slider:before {
        transform: translateX(18px);
      }
      
      #staypoint-list {
        flex-grow: 1;
        overflow-y: auto;
        padding: 0;
        margin: 0;
      }
      .staypoint-item {
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }
      .staypoint-item:hover {
        background-color: #e9ecef;
      }
      .staypoint-item b {
        font-size: 0.9em;
        display: block;
        margin-bottom: 5px;
      }
      .staypoint-item small {
        color: #6c757d;
        display: block;
        font-size: 0.8em;
      }
      .staypoint-item .marker-icon {
        max-width: 20px;
        margin-right: 10px;
        vertical-align: middle;
        display: inline-block;
        float: right;
      }
      #map-controls {
        position: absolute;
        bottom: 20px;
        left: 10px;
        z-index: 401;
        display: flex;
        flex-direction: column;
      }
      .map-btn {
        background-color: white;
        border: 2px solid rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        width: 34px;
        height: 34px;
        line-height: 30px;
        text-align: center;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
        margin-top: 10px;
      }
      .map-btn:hover {
        background-color: #f4f4f4;
      }
      
      #panel-toggle-btn {
        display: none; /* Hidden by default */
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 800; /* High z-index to be on top of map but below panel */
      }
      #map-overlay {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
      }
      
      /* Mobile Responsive Styles */
      @media (max-width: 1024px) {
        #side-panel {
          position: absolute;
          right: 0;
          top: 0;
          z-index: 1000;
          transform: translateX(100%);
        }
        #side-panel.is-open {
          transform: translateX(0);
        }
        #panel-toggle-btn {
          display: block; /* Show toggle button on mobile */
        }
      }
    </style>
{% endblock %}

{% block content %}
    <div id="app-container">
      <div id="map-container">
        <div id="map"></div>
        <div id="map-overlay"></div>
        <button id="panel-toggle-btn" class="map-btn" title="Show list"><i class="fa-solid fa-list"></i></button>
      </div>
      <div id="side-panel">
        <div id="controls-container">
          <div class="date-picker-wrapper">
            <i class="fa-solid fa-calendar-days date-picker-icon"></i>
            <input class="flatpickr-input" type="text" id="date-range-picker" placeholder="Select a date range..." />
          </div>
          <div class="toggle-switch">
            <label for="places-toggle">Show Places</label>
            <label class="switch">
              <input type="checkbox" id="places-toggle" />
              <span class="slider"></span>
            </label>
          </div>
          <div class="toggle-switch hidden" id="radius-toggle-row">
            <label for="radius-toggle">Show Place Radii</label>
            <label class="switch">
              <input type="checkbox" id="radius-toggle" />
              <span class="slider"></span>
            </label>
          </div>
        </div>
        <div id="staypoint-list"></div>
      </div>
    </div>

    <div id="map-controls">
      <button id="fit-bounds-btn" class="map-btn" title="Fit to all staypoints"><i class="fa-solid fa-expand"></i></button>
      <button id="current-location-btn" class="map-btn" title="Go to my location"><i class="fa-solid fa-location-crosshairs"></i></button>
    </div>

    {{ staypoints_geojson|json_script:'staypoints-data' }}
{% endblock %}

{% block extra_js %}
    <!-- Leaflet.js JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <!-- flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <script type="text/javascript">
      // Set active navigation item
      document.addEventListener('DOMContentLoaded', function() {
        const staypointsLink = document.querySelector('a[href="/manage/locations/staypoint-map/"]');
        if (staypointsLink) {
          staypointsLink.classList.add('active');
        }
      });

      const map = L.map('map').setView([40.7128, -74.006], 12) // Default to NYC
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
      }).addTo(map)
      
      let staypointsLayer, placesLayer
      let featureLayers = []
      const sidePanel = document.getElementById('side-panel')
      const panelToggleBtn = document.getElementById('panel-toggle-btn')
      const mapOverlay = document.getElementById('map-overlay')
      const staypointListContainer = document.getElementById('staypoint-list')
      const radiusToggle = document.getElementById('radius-toggle')
      const placesToggle = document.getElementById('places-toggle')
      const radiusToggleRow = document.getElementById('radius-toggle-row')
      let placeRadii = []
      
      // --- Custom Icons ---
      const staypointIcon = L.icon({
        iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
      })
      const questionableStaypointIcon = L.icon({
        iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-yellow.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
      })
      const placeIcon = L.icon({
        iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-black.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
      })
      const userLocationIcon = L.icon({
        iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
      })
      
      function formatStaypointDate(dateString) {
        if (!dateString) return 'N/A'
        const date = new Date(dateString)
        const options = { weekday: 'short', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true }
        return new Intl.DateTimeFormat('en-US', options)
          .format(date)
          .replace(' at ', ', ')
          .replace(/ (\wM)$/, '$1'.toLowerCase())
      }
      
      function updateMapWithData(geojson) {
        if (staypointsLayer) map.removeLayer(staypointsLayer)
        staypointListContainer.innerHTML = ''
        featureLayers = []
      
        if (geojson?.features?.length > 0) {
          staypointsLayer = L.geoJSON(geojson, {
            pointToLayer: (feature, latlng) => L.marker(latlng, { icon: staypointIcon }),
            onEachFeature: (feature, layer) => {
              const props = feature.properties
              const featureIndex = featureLayers.length
              const icon = props.place_name !== '--' ? staypointIcon : questionableStaypointIcon
              layer.setIcon(icon)
              featureLayers.push(layer)
              layer.bindPopup(`<b>${props.place_name}</b><br>Arrival: ${formatStaypointDate(props.arrival)}<br>Departure: ${formatStaypointDate(props.departure)}<br>Duration: ${props.duration_minutes} minutes`)
      
              const listItem = document.createElement('div')
              listItem.className = 'staypoint-item'
              listItem.dataset.featureId = featureIndex
              listItem.innerHTML = `<img src="${icon.options.iconUrl}" alt="Marker Icon" class="marker-icon"> <b>${props.place_name}</b><small>Arrival: ${formatStaypointDate(props.arrival)}</small><small>Departure: ${formatStaypointDate(props.departure)}</small><small>Duration: ${props.duration_minutes} minutes</small>`
              staypointListContainer.appendChild(listItem)
            }
          }).addTo(map)
          map.fitBounds(staypointsLayer.getBounds().pad(0.1))
        } else {
          staypointListContainer.innerHTML = '<div class="staypoint-item">No stay points found.</div>'
        }
      }
      
      function fetchAndDisplayPlaces() {
        // Don't fetch places if toggle is off
        if (!placesToggle.checked) {
          if (placesLayer) {
            map.removeLayer(placesLayer)
            placesLayer = null
          }
          // Clear place radii
          placeRadii.forEach((circle) => map.removeLayer(circle))
          placeRadii = []
          return
        }

        const bounds = map.getBounds()
        const center = map.getCenter()
        const payload = { lat: center.lat, lng: center.lng, unit: 1, bounds: { ne_lat: bounds.getNorthEast().lat, ne_lng: bounds.getNorthEast().lng, sw_lat: bounds.getSouthWest().lat, sw_lng: bounds.getSouthWest().lng } }
      
        fetch('/api/places/search/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-CSRFToken': '{{ csrf_token }}' },
          body: JSON.stringify(payload)
        })
          .then((response) => response.json())
          .then((data) => {
            const places = data.results || []
            if (placesLayer) map.removeLayer(placesLayer)
            placeRadii.forEach((circle) => map.removeLayer(circle))
            placeRadii = []
            placesLayer = L.layerGroup()
      
            places.forEach((place) => {
              if (place.location?.lat && place.location?.lng) {
                const marker = L.marker([place.location.lat, place.location.lng], { icon: placeIcon }).bindPopup(`<b>${place.name}</b><br>${place.address}`)
                placesLayer.addLayer(marker)
      
                if (place.radius) {
                  const circle = L.circle([place.location.lat, place.location.lng], { radius: place.radius, color: '#0d6efd', weight: 1, fillOpacity: 0.1 })
                  placeRadii.push(circle)
                }
              }
            })
            placesLayer.addTo(map)
            updateRadiiVisibility()
          })
          .catch((error) => console.error('Error fetching places:', error))
      }
      
      function updateRadiiVisibility() {
        const showRadii = radiusToggle.checked
        placeRadii.forEach((circle) => {
          if (showRadii) {
            if (!map.hasLayer(circle)) map.addLayer(circle)
          } else {
            if (map.hasLayer(circle)) map.removeLayer(circle)
          }
        })
      }

      function updateRadiiToggleVisibility() {
        if (placesToggle.checked) {
          radiusToggleRow.classList.remove('hidden')
        } else {
          radiusToggleRow.classList.add('hidden')
        }
      }
      
      function togglePanel(isOpen) {
        if (isOpen) {
          sidePanel.classList.add('is-open')
          mapOverlay.style.display = 'block'
        } else {
          sidePanel.classList.remove('is-open')
          mapOverlay.style.display = 'none'
        }
        // Allow map to redraw itself correctly
        setTimeout(() => map.invalidateSize(), 300)
      }
      
      // --- Initial Load ---
      const initialGeojsonData = JSON.parse(document.getElementById('staypoints-data').textContent)
      updateMapWithData(initialGeojsonData)
      map.on('moveend', fetchAndDisplayPlaces)
      fetchAndDisplayPlaces()
      updateRadiiToggleVisibility() // Set initial visibility state
      
      // --- Event Listeners ---
      radiusToggle.addEventListener('change', updateRadiiVisibility)
      placesToggle.addEventListener('change', () => {
        fetchAndDisplayPlaces()
        updateRadiiToggleVisibility()
      })
      panelToggleBtn.addEventListener('click', () => togglePanel(!sidePanel.classList.contains('is-open')))
      mapOverlay.addEventListener('click', () => togglePanel(false))
      
      document.getElementById('fit-bounds-btn').addEventListener('click', () => {
        if (staypointsLayer?.getLayers().length > 0) {
          map.fitBounds(staypointsLayer.getBounds().pad(0.1))
        }
      })
      
      document.getElementById('current-location-btn').addEventListener('click', () => {
        navigator.geolocation.getCurrentPosition(
          (pos) => {
            const { latitude: lat, longitude: lon } = pos.coords
            map.setView([lat, lon], 15)
            L.marker([lat, lon], { icon: userLocationIcon }).addTo(map).bindPopup('Your current location.').openPopup()
          },
          (err) => alert(`Error: ${err.message}`)
        )
      })
      
      staypointListContainer.addEventListener('click', (e) => {
        const listItem = e.target.closest('.staypoint-item')
        if (listItem?.dataset.featureId) {
          const targetLayer = featureLayers[parseInt(listItem.dataset.featureId, 10)]
          if (targetLayer) {
            map.flyTo(targetLayer.getLatLng(), 16)
            targetLayer.openPopup()
            if (window.innerWidth <= 768) {
              togglePanel(false)
            }
          }
        }
      })
      
      flatpickr('#date-range-picker', {
        mode: 'range',
        altInput: true,
        altFormat: 'F j', // How the date is displayed: "September 5"
        dateFormat: 'Y-m-d', // How the date is stored and sent to the server
        defaultDate: [new Date().fp_incr(-6), new Date()],
        onClose: (selectedDates) => {
          if (selectedDates.length === 2) {
            const [startStr, endStr] = selectedDates.map((d) => d.toISOString().split('T')[0])
            fetch(`/manage/locations/api/staypoints/?start=${startStr}&end=${endStr}`)
              .then((response) => response.json())
              .then((data) => updateMapWithData(data))
              .catch((error) => console.error('Error fetching staypoint data:', error))
          }
        }
      })
      // End of JavaScript
    </script>
{% endblock %}
