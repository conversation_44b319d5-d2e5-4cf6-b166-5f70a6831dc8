from django.contrib import admin
from locations.models import BlacklistedDevices, Location, StayPoint
from django.contrib.gis.admin import GISModelAdmin
from import_export.admin import ImportExportModelAdmin
from import_export import resources


class LocationResource(resources.ModelResource):
    class Meta:
        model = Location


class LocationAdmin(GISModelAdmin, ImportExportModelAdmin):
    ordering = ["-timestamp"]
    list_filter = ["timestamp", "action",
                   "is_moving", "device_id", "processed", "created"]
    list_display = ["point", "profile",
                    "device_id", "timestamp", "created", "place", "action", "accuracy", "is_moving", "processed"]
    autocomplete_fields = ["profile"]
    resource_class = LocationResource
    search_fields = ["device_id"]
    actions = ["mark_processed", "mark_unprocessed"]

    def mark_processed(self, request, queryset):
        queryset.update(processed=True)

    def mark_unprocessed(self, request, queryset):
        queryset.update(processed=False)


class BlacklistedDevicesAdmin(admin.ModelAdmin):
    ordering = ["created"]
    list_display = ["device_id", "profile", "created"]
    autocomplete_fields = ["profile"]
    search_fields = ["device_id"]


class StayPointAdmin(GISModelAdmin):
    ordering = ["-start_time"]
    list_display = ["point", "profile", "start_time",
                    "end_time", "duration", "place", "device_id", "created_at"]
    autocomplete_fields = ["profile", "place"]
    search_fields = ["device_id"]
    list_filter = ["start_time", "end_time", "created_at",]


admin.site.register(Location, LocationAdmin)
admin.site.register(BlacklistedDevices, BlacklistedDevicesAdmin)
admin.site.register(StayPoint, StayPointAdmin)
