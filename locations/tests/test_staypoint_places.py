from django.test import TestCase
from django.contrib.gis.geos import Point
from datetime import datetime, timezone
from locations.models import StayPoint
from locations.services import LocationServices
from places.models import Place
from profiles.models import Profile


class StayPointPlaceAssociationTest(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create a test profile
        self.profile = Profile.objects.create(
            hash_id="test-profile-123",
            timezone="America/New_York"
        )
        
        # Create a test place
        self.place = Place.objects.create(
            name="Test Restaurant",
            coordinates=Point(-75.1652, 39.9526, srid=4326),  # Philadelphia coordinates
            radius=100,  # 100 meter radius
            browsable=True,
            approved=True
        )
        
    def test_create_staypoint_with_place_association(self):
        """Test that new staypoints are automatically associated with nearby places"""
        # Create staypoint data near the test place
        staypoint_data = [{
            'latitude': 39.9525,  # Very close to the place
            'longitude': -75.1651,
            'arrival_time': datetime(2024, 1, 1, 12, 0, tzinfo=timezone.utc),
            'departure_time': datetime(2024, 1, 1, 13, 0, tzinfo=timezone.utc),
            'device_id': 'test-device'
        }]
        
        # Create staypoints using the service
        created_staypoints = LocationServices.create_staypoints_from_data(
            profile=self.profile,
            staypoints_data=staypoint_data
        )
        
        # Verify staypoint was created and associated with place
        self.assertEqual(len(created_staypoints), 1)
        staypoint = created_staypoints[0]
        self.assertIsNotNone(staypoint.place)
        self.assertEqual(staypoint.place.name, "Test Restaurant")
        
    def test_create_staypoint_without_nearby_place(self):
        """Test that staypoints far from places don't get associated"""
        # Create staypoint data far from any place
        staypoint_data = [{
            'latitude': 40.7128,  # NYC coordinates, far from our test place
            'longitude': -74.0060,
            'arrival_time': datetime(2024, 1, 1, 12, 0, tzinfo=timezone.utc),
            'departure_time': datetime(2024, 1, 1, 13, 0, tzinfo=timezone.utc),
            'device_id': 'test-device'
        }]
        
        # Create staypoints using the service
        created_staypoints = LocationServices.create_staypoints_from_data(
            profile=self.profile,
            staypoints_data=staypoint_data
        )
        
        # Verify staypoint was created but not associated with place
        self.assertEqual(len(created_staypoints), 1)
        staypoint = created_staypoints[0]
        self.assertIsNone(staypoint.place)
        
    def test_update_existing_staypoints_with_places(self):
        """Test that existing staypoints without places can be updated"""
        # Create a staypoint without place association
        staypoint = StayPoint.objects.create(
            profile=self.profile,
            point=Point(-75.1651, 39.9525, srid=4326),  # Near our test place
            start_time=datetime(2024, 1, 1, 12, 0, tzinfo=timezone.utc),
            end_time=datetime(2024, 1, 1, 13, 0, tzinfo=timezone.utc),
            device_id='test-device'
        )
        
        # Verify it starts without a place
        self.assertIsNone(staypoint.place)
        
        # Update staypoints with places
        updated_count = LocationServices.update_staypoints_with_places(
            profile=self.profile
        )
        
        # Verify the staypoint was updated
        self.assertEqual(updated_count, 1)
        staypoint.refresh_from_db()
        self.assertIsNotNone(staypoint.place)
        self.assertEqual(staypoint.place.name, "Test Restaurant")
