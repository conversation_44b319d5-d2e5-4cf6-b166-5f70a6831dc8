from django.contrib.gis.db import models

from places.models import Place
from profiles.models import Profile


class StayPoint(models.Model):  # Define your StayPoint model
    """
    Represents a stay point.
    """
    point = models.PointField(geography=True, null=False, srid=4326)
    profile = models.ForeignKey('profiles.Profile', on_delete=models.CASCADE)
    device_id = models.CharField(
        max_length=255, null=True, blank=True)  # Add device_id
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    place = models.ForeignKey(
        'places.Place', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # Return the duration in whole minutes of the staypoint
    def duration(self):
        return (self.end_time - self.start_time).total_seconds() / 60

    def __str__(self):
        return f"StayPoint at {self.point} from {self.start_time} to {self.end_time}"


class Location(models.Model):
    point = models.PointField(geography=True, null=True, blank=True, srid=4326)
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE)
    device_id = models.CharField(max_length=255)
    timestamp = models.DateTimeField(null=True, blank=True)
    geofence = models.CharField(max_length=255, null=True, blank=True)
    action = models.CharField(max_length=255, null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    accuracy = models.FloatField(null=True, blank=True)
    speed = models.FloatField(null=True, blank=False)
    is_moving = models.BooleanField(null=True)
    processed = models.BooleanField(default=False, db_index=True)

    def location(self) -> dict:
        return {"lat": self.point.y,
                "lng": self.point.x}

    def place(self) -> Place:
        if self.geofence:
            return Place.objects.get(hash_id=self.geofence)
        else:
            return None

    def __str__(self):
        return self.point.__str__()

    class Meta:
        ordering = ["-timestamp"]


class BlacklistedDevices(models.Model):
    device_id = models.CharField(max_length=255, db_index=True)
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
