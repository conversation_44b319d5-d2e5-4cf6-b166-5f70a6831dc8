import json

from django.http import JsonResponse
from locations.models import StayPoint
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from re import M
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.decorators import permission_classes
from guytrax.pagination import LongListPagination
from guytrax.serializers import DateRangeSerializer, DateTimeSerializer, PointSerializer, RadiusRequestSerializer
from locations.models import BlacklistedDevices, Location
from locations.serializers import *
from locations.services import LocationServices, process_location_data
from places.serializers import PlaceVisitSerializer
from profiles.models import Profile
from django.contrib.gis.geos import Point
from rest_framework.decorators import action
from places.services import PlacesServices
from places.models import PlaceVisit, Place
from django.utils import timezone
from datetime import datetime, timedelta


class LocationsViewSet(viewsets.ModelViewSet):
    permission_classes = (IsAdminUser,)
    queryset = Location.objects.all().order_by('-created')
    serializer_class = LocationSerializer
    serializer_classes = {
        'list': LocationSerializer,
        'retrieve': LocationSerializer,
        'create': CreateLocationSerializer,
        'update': LocationSerializer,
        'partial_update': LocationSerializer,
        'destroy': LocationSerializer,
        'geofences': PointSerializer,
        'process': ProcessLocationSerializer,
        'staypoints': DateRangeSerializer,
    }
    pagination_class = LongListPagination

    def get_serializer_class(self):
        if self.action in self.serializer_classes:
            return self.serializer_classes[self.action]
        return super.get_serializer_class()

    def get_queryset(self):
        queryset = Location.objects.all().order_by(
            '-created').select_related("profile").prefetch_related("place__primary_type")
        if not self.request.user.is_superuser:
            queryset = Location.objects.none()
        return queryset

    def create(self, request, *args, **kwargs):
        data = request.data
        try:
            point = data.get("point")
            if point is None:
                point_values = data.get('coordinates')
                point = Point(point_values[0], point_values[1], srid=4326)
                request.data['point'] = point
        except Exception as e:
            print(e)
        device_id = request.headers.get("DeviceId", None)
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            profile = serializer.validated_data.get("profile")
            profile.coordinates = point
            is_blacklisted = BlacklistedDevices.objects.filter(
                device_id=device_id, profile=profile).exists()
            if is_blacklisted:
                print("Blacklisted device attempting to log location: ", point)
                return Response("Device is blacklisted", status=status.HTTP_200_OK)
            profile.save()
            self.perform_create(serializer)
            action = serializer.validated_data.get("action", None)
            if action == "DWELL" or action == "EXIT":
                process_location_data(profile.hash_id)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            print(request.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path='device/' + r'(?P<deviceId>[A-Z0-9-]+)', permission_classes=[IsAuthenticated])
    def list_by_profile(self, request, deviceId=None):
        profile = request.user.profiles.first()
        if profile is None:
            return Response("No profile found", status=status.HTTP_400_BAD_REQUEST)
        queryset = Location.objects.filter(profile=profile, device_id=deviceId).order_by(
            '-created').select_related("profile")
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = LocationListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            return Response("No results", status=status.HTTP_404_NOT_FOUND)

    @list_by_profile.mapping.post
    def filter_by_profile(self, request, deviceId=None):
        profile = request.user.profiles.first()
        serializer = DateRangeSerializer(data=request.data)
        if serializer.is_valid():
            start_date = serializer.validated_data.get(
                "start_date", timezone.now() - timedelta(days=7))
            end_date = serializer.validated_data.get(
                "end_date", timezone.now())
            queryset = Location.objects.filter(profile=profile, device_id=deviceId, timestamp__gte=start_date, timestamp__lte=end_date).order_by(
                '-created').select_related("profile")
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = LocationListSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            else:
                return Response("No results", status=status.HTTP_404_NOT_FOUND)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], serializer_class=RadiusRequestSerializer, permission_classes=[IsAuthenticated])
    def geofences(self, request):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            lat = serializer.data.get("lat")
            lng = serializer.data.get("lng")
            distance = serializer.data.get("distance", 5.0)
            point = Point(lng, lat, srid=4326)
            places = PlacesServices.nearest_places(
                point=point,
                distance=distance,
                count=serializer.data.get("count", 50)
            )
            response = GeoFenceSerializer(places, many=True)
            return Response({"points": response.data})
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], serializer_class=DateRangeSerializer, permission_classes=[IsAuthenticated])
    def staypoints(self, request):
        profile = self.request.user.profiles.first()
        serializer = DateRangeSerializer(data=request.data)
        if serializer.is_valid():
            start_date = serializer.validated_data.get(
                "start_date", timezone.now() - timedelta(days=7))
            end_date = serializer.validated_data.get(
                "end_date", timezone.now())
            staypoints = StayPoint.objects.filter(
                profile=profile,
                start_time__gte=start_date,
                end_time__lte=end_date
            ).order_by('-start_time')
            paginated = self.paginate_queryset(staypoints)
            serializer = StayPointSerializer(paginated, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], serializer_class=ProcessLocationSerializer)
    def process(self, request):
        profile = request.user.profiles.first()
        serializer = ProcessLocationSerializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            start_date = serializer.validated_data.get(
                "start_date", timezone.now() - timedelta(days=30))
            end_date = serializer.validated_data.get(
                "end_date", timezone.now())
            reprocess = serializer.validated_data.get("reprocess", False)
            try:
                created_staypoints, placeVisits = LocationServices().process_location_data(
                    profile_id=profile.hash_id,
                    start_date=start_date,
                    end_date=end_date,
                    re_process=reprocess
                )
            except Exception as e:
                print(f"Error processing location data: {e}")
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            return Response(
                {
                    "placeVisits": PlaceVisitSerializer(placeVisits, many=True).data,
                    "staypoints": StayPointSerializer(created_staypoints, many=True).data
                },
                status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
