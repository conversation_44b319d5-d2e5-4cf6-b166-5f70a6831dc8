
from rest_framework import serializers

from places.serializers import PlaceTypeSerializer
from tags.serializers import TagSerializer
from vibes.models import Vibe


class VibeSerializer(serializers.ModelSerializer):

    class Meta:
        fields = ["id", "name", "descsription",
                  "image", "material_icon", "color"]
        model = Vibe


class VibeDetailSerializer(VibeSerializer):
    types = PlaceTypeSerializer(many=True)
    tags = TagSerializer(many=True)

    class Meta(VibeSerializer.Meta):
        fields = VibeSerializer.Meta.fields + \
            ["descsription", "order", "tags", "types"]
