address==0.1.1
annotated-types==0.7.0
appnope==0.1.4
asgiref==3.8.1
asttokens==3.0.0
attrs==25.1.0
backcall==0.2.0
beautifulsoup4==4.13.3
bleach==6.2.0
CacheControl==0.14.2
cachetools==5.5.1
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cryptography==44.0.1
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.18
diff-match-patch==20241021
Django==5.1.6
django-advanced-filters==2.0.0
django-allauth==65.4.1
django-background-tasks==1.2.8
django-cleanup==9.0.0
django-colorfield==0.12.0
django-cors-headers==4.7.0
django-debug-toolbar==5.0.1
django-environ==0.12.0
django-extensions==3.2.3
django-filter==23.5
django-geojson==4.1.0
django-hashid-field==3.4.1
django-hashids==0.7.0
django-import-export==4.3.9
django-ipware==7.0.1
django-oauth-toolkit==3.0.1
django-push-notifications==3.2.0
django-recurrence==1.11.1
django-rest-authemail==2.1.7
django-rest-knox==5.0.2
django-storages==1.14.4
django-timezone-field==7.1
djangorestframework==3.15.2
djangorestframework-gis==1.1
docopt==0.6.2
drf-material==0.3.3
drf-redesign==0.4.0
drf-restwind==0.1.6
drf-spectacular==0.28.0
dynaconf==3.2.7
ecdsa==0.19.1
environ==1.0
executing==2.2.0
fake-factory==9999.9.9
Faker==36.1.1
fastjsonschema==2.21.1
fcm-django==2.2.1
firebase-admin==6.6.0
future==1.0.0
geographiclib==2.0
geopy==2.4.1
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.161.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-appengine-logging==1.6.0
google-cloud-audit-log==0.3.0
google-cloud-core==2.4.1
google-cloud-firestore==2.20.1
google-cloud-logging==3.11.4
google-cloud-secret-manager==2.23.0
google-cloud-storage==3.0.0
google-cloud-vision==3.10.1
google-crc32c==1.6.0
google-genai==1.2.0
google-generativeai==0.8.4
google-resumable-media==2.7.2
googleapis-common-protos==1.67.0
grpc-google-iam-v1==0.14.0
grpcio==1.70.0
grpcio-status==1.70.0
gunicorn==23.0.0
h3==4.2.1
hashids==1.3.1
haversine==2.9.0
httplib2==0.22.0
hvac==2.3.0
idna==3.10
importlib_metadata==8.5.0
inflection==0.5.1
ipython==8.12.3
jedi==0.19.2
Jinja2==3.1.5
joblib==1.5.2
jsonformer==0.12.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
jwcrypto==1.5.6
knox==0.1.14
loguru==0.7.3
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mistune==3.1.1
msgpack==1.1.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
numpy==2.2.3
oauthlib==3.2.2
opentelemetry-api==1.30.0
packaging==24.2
pandas==2.3.2
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
pickleshare==0.7.5
pillow==11.1.0
pipreqs==0.5.0
platformdirs==4.3.6
prompt_toolkit==3.0.50
proto-plus==1.26.0
protobuf==5.29.3
psycopg==3.2.4
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycountry==24.6.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.1
python-dateutil==2.9.0.post0
python-ipware==3.0.0
python-jose==3.5.0
python-json-logger==3.3.0
pytz==2025.1
PyYAML==6.0.2
pyzmq==26.2.1
referencing==0.36.2
requests==2.32.3
rpds-py==0.22.3
rsa==4.9
scikit-learn==1.7.2
scipy==1.16.1
shortuuid==1.0.13
simplejson==3.19.3
six==1.17.0
soupsieve==2.6
sqlparse==0.5.3
stack-data==0.6.3
swapper==1.4.0
tablib==3.8.0
termcolor==2.5.0
threadpoolctl==3.6.0
timezonefinder==6.5.8
tinycss2==1.4.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.3.0
validators==0.34.0
wcwidth==0.2.13
webencodings==0.5.1
websockets==14.2
wrapt==1.17.2
yarg==0.1.9
zipp==3.21.0
