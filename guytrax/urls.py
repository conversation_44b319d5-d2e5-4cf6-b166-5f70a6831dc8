"""
URL configuration for guytrax project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.conf.urls import include
from rest_framework import routers
from admin.views import AdminViewSet
from areas.views import AreaViewSet
from automations.views import AutomationViewSet
from dj.views import DJViewSet, GenreViewSet
from events.views import EventInstanceViewSet, EventViewSet, FestivalViewSet
from google.views import GoogleViewSet
from auth import authentication, google, apple
from explore.views import ExploreViewSet
from producers.views import ProducerViewSet
from search.views import SearchView
from locations.api import LocationsViewSet
from places.api import PlaceTypeViewSet, PlaceViewSet
from profiles.views import ProfileViewSet, MyProfileViewSet
from django.conf import settings
from django.conf.urls.static import static
from oauth2_provider import urls as oauth2_urls
from reviews.views import ReviewViewSet, CheckInViewSet
from tags.views import SuggestedTagViewSet, TagViewSet
from users.views import UserViewSet
from vibes.views import VibesViewSet
from notifications.views import NotificationsViewSet
from . import views
from django.views.i18n import JavaScriptCatalog

api = routers.DefaultRouter()
api.register(r'areas', AreaViewSet)
api.register(r'profiles', ProfileViewSet, basename='profiles')
api.register(r'places', PlaceViewSet)
api.register(r'place_types', PlaceTypeViewSet)
api.register(r'events', EventInstanceViewSet, basename='events')
api.register(r'event', EventViewSet, basename='event')
api.register(r'festivals', FestivalViewSet)
api.register(r'users', UserViewSet)
api.register(r'tags', TagViewSet)
api.register(r'suggested-tags', SuggestedTagViewSet)
api.register(r'locations', LocationsViewSet)
api.register(r'vibes', VibesViewSet)
api.register(r'check-ins', CheckInViewSet)
api.register(r'reviews', ReviewViewSet)
api.register(r'djs', DJViewSet)
api.register(r'producers', ProducerViewSet)
api.register(r'genres', GenreViewSet)
api.register(r'auth', authentication.AuthenticationViewSet, basename='auth')
api.register(r'explore', ExploreViewSet, basename='explore')
api.register(r'', SearchView, basename='search')
api.register(r'me', MyProfileViewSet, basename='me')
api.register(r'notifications', NotificationsViewSet)
api.register(r'google', GoogleViewSet, basename='google')
api.register(r'admin', AdminViewSet, basename='admin')
api.register(r'admin/automations', AutomationViewSet,
             basename='automations')


js_info_dict = {
    'packages': ('recurrence', ),
}

urlpatterns = [
    path('', views.index, name="index"),
    path('api/', include(api.urls)),
    path('api/google/login/', google.login_api, name='google_login_api'),
    path('api/apple/login/', apple.login_api, name='apple_login_api'),
    path('o/token/', authentication.TokenView.as_view()),
    path('o/', include(oauth2_urls)),
    path('admin/', admin.site.urls),
    path('advanced_filters/', include('advanced_filters.urls')),
    path('privacy', views.privacy, name="privacy"),
    path('terms', views.terms, name="terms"),
    path('health', views.index, name="health"),
    path('status', views.index, name="status"),
    path('jsi18n/', JavaScriptCatalog.as_view(), name='javascript-catalog'),
    path('manage/', include('management.urls')),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
# urlpatterns += [path(r'^jsi18n/$', JavaScriptCatalog.as_view(), js_info_dict)]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG_TOOLBAR:
    import debug_toolbar
    urlpatterns += [
        path('__debug__/', include(debug_toolbar.urls)),
    ]
