ext{
    appCompatVersion    = "1.4.2"
    playServicesLocationVersion = "21.3.0"
}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "${project(':flutter_background_geolocation').projectDir}/libs" }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "${project(':background_fetch').projectDir}/libs" }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

