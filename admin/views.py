from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser
from admin.serializers import EventToDoSerializer, PlaceToDoSerializer
from events.models import Event, EventPhoto
from places.models import Place, PlacePhoto
from django.db.models import Q
from places.serializers import PlacePhotoSerializer
from profiles.models import ProfilePhoto
from rest_framework.pagination import PageNumberPagination
from rest_framework import serializers
from drf_spectacular.utils import extend_schema


@extend_schema(exclude=True)
class AdminViewSet(viewsets.ViewSet):
    permission_classes = [IsAdminUser]
    paginator = PageNumberPagination()
    page_size = 50
    paginator.page_size = page_size

    @action(detail=False, methods=['get'], url_path='todos')
    def todos(self, request):
        place_todos_count = Place.objects.filter(Q(logo='') | Q(
            type__isnull=True) | Q(tags__isnull=True)).values("id").distinct().count()
        return Response({'place_todos_count': place_todos_count}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'], url_path="todo/places")
    def place_todos(self, request):
        places = self.paginator.paginate_queryset(Place.objects.
                                                  filter(Q(logo='') | Q(type__isnull=True) | Q(tags__isnull=True) | Q(hours__isnull=True)).distinct().
                                                  prefetch_related("primary_type", "tags").all().order_by('name'), request)
        serializer = PlaceToDoSerializer(places, many=True)
        return self.paginator.get_paginated_response(serializer.data)

    @action(detail=False, methods=['get'], url_path="todo/events")
    def event_todos(self, request):
        events = self.paginator.paginate_queryset(Event.objects.
                                                  filter(Q(photo='') | Q(tags__isnull=True) | (Q(cost__isnull=True, is_free=False))).
                                                  prefetch_related("tags").all().order_by('updated'), request)
        serializer = EventToDoSerializer(events, many=True)
        return self.paginator.get_paginated_response(serializer.data)

    @action(detail=False, methods=['get'], url_path="todo/profiles/photos")
    def profile_photo_todos(self, request):
        from profiles.serializers import ProfilePhotoSerializer
        photos = self.paginator.paginate_queryset(ProfilePhoto.objects.
                                                  filter(approved=False, rejected_date__isnull=True).select_related('profile').all().order_by('uploaded'), request)
        serializer = ProfilePhotoSerializer(photos, many=True)
        return self.paginator.get_paginated_response(serializer.data)

    @action(detail=False, methods=['get'], url_path="todo/places/photos")
    def place_photo_todos(self, request):
        photos = self.paginator.paginate_queryset(PlacePhoto.objects.
                                                  filter(approved=False, rejected_date__isnull=True).select_related('place').all().order_by('uploaded'), request)
        serializer = PlacePhotoSerializer(photos, many=True)
        return self.paginator.get_paginated_response(serializer.data)

    @action(detail=False, methods=['get'], url_path="todo/events/photos")
    def event_photo_todos(self, request):
        from events.serializers import EventPhotoSerializer
        photos = self.paginator.paginate_queryset(EventPhoto.objects.
                                                  filter(approved=False, rejected_date__isnull=True).select_related('event').all().order_by('uploaded'), request)
        serializer = EventPhotoSerializer(photos, many=True)
        return self.paginator.get_paginated_response(serializer.data)

    @action(detail=False, methods=['get'], url_path="places/addresses")
    def place_addresses(self, request):
        addresses = Place.objects.all().order_by(
            'address').values_list('address', flat=True).distinct()
        return Response(addresses, status=status.HTTP_200_OK)
