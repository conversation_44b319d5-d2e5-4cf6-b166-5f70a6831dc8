from rest_framework import viewsets, status, parsers, filters
from rest_framework.permissions import IsAuthenticated, IsAdminUser

from guytrax.pagination import ShortListPagination
from reviews.models import CheckIn, Review
from reviews.serializers import CheckInSerializer, ReviewRequestSerializer, ReviewSerializer


class CheckInViewSet(viewsets.ModelViewSet):
    permission_classes = (IsAuthenticated, IsAdminUser)
    queryset = CheckIn.objects.all().order_by(
        "-created").prefetch_related("profile", "place", 'place__primary_type', "event")
    serializer_class = CheckInSerializer
    pagination_class = ShortListPagination


class ReviewViewSet(viewsets.ModelViewSet):
    permission_classes = (IsAuthenticated, IsAdminUser)
    queryset = Review.objects.all().order_by(
        "-created").prefetch_related("categories", "categories__category", "availabilities", "availabilities__category", "profile", "place", "place__primary_type", "event")
    serializer_class = ReviewSerializer

    def get_serializer_class(self):
        if self.action == 'create' or self.action == 'update' or self.action == 'partial_update':
            return ReviewRequestSerializer
        return super().get_serializer_class()
