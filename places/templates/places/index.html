{% extends 'base.html' %}

{% block title %}
  Places Map - GTX
{% endblock %}

{% block extra_css %}
  <!-- Leaflet.js CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" xintegrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <style>
    /* Override base template styles for full-screen map */
    body {
      display: flex;
      flex-direction: column;
      height: 100vh;
      margin: 0;
      overflow: hidden;
    }
    
    .navbar-custom {
      height: auto;
    }
    
    .main-content {
      padding: 0 !important;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0; /* Important for flex child to shrink */
    }
    
    .container-fluid {
      padding: 0 !important;
      max-width: none !important;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
    
    #app-container {
      display: flex;
      flex: 1;
      min-height: 0;
      width: 100%;
    }
    #map-container {
      flex-grow: 1;
      height: 100%;
      position: relative;
    }
    #map {
      height: 100%;
      width: 100%;
    }
    #side-panel {
      width: 300px;
      min-width: 200px;
      height: 100%;
      background-color: #f8f9fa;
      border-left: 1px solid #dee2e6;
      display: flex;
      flex-direction: column;
      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease-in-out;
    }
    #controls-container {
      padding: 15px;
      border-bottom: 1px solid #dee2e6;
      background-color: white;
    }
    
    .search-wrapper {
      position: relative;
      margin-bottom: 15px;
    }
    
    .search-wrapper input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .search-wrapper input:focus {
      border-color: #86b7fe;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .search-wrapper .search-icon {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      pointer-events: none;
    }
    
    .toggle-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: opacity 0.3s ease, max-height 0.3s ease, margin 0.3s ease, padding 0.3s ease;
      overflow: hidden;
      max-height: 50px;
      padding: 10px 0;
    }
    
    .toggle-switch.hidden {
      opacity: 0;
      max-height: 0;
      margin-bottom: 0;
      padding: 0;
    }
    .toggle-switch label {
      color: #495057;
    }
    .toggle-switch .switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 22px;
    }
    .toggle-switch .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: 0.4s;
      border-radius: 22px;
    }
    .slider:before {
      position: absolute;
      content: '';
      height: 18px;
      width: 18px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: 0.4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #0d6efd;
    }
    input:checked + .slider:before {
      transform: translateX(18px);
    }
    
    #place-list {
      flex-grow: 1;
      overflow-y: auto;
      padding: 0;
      margin: 0;
    }
    .place-item {
      padding: 10px 15px;
      border-bottom: 1px solid #dee2e6;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .place-item:hover {
      background-color: #e9ecef;
    }
    .place-item:last-child {
      border-bottom: none;
    }
    .place-name {
      font-weight: 600;
      color: #212529;
      margin-bottom: 4px;
    }
    .place-address {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 4px;
    }
    .place-distance {
      font-size: 11px;
      color: #868e96;
    }
    
    #map-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 1000;
      display: none;
    }
    
    .map-btn {
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 8px;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      transition: background-color 0.2s;
    }
    .map-btn:hover {
      background-color: #f8f9fa;
    }
    
    #panel-toggle-btn {
      top: 10px;
      right: 10px;
      display: none;
    }
    
    #map-controls {
      position: absolute;
      top: 10px;
      left: 10px;
      display: flex;
      flex-direction: column;
      gap: 5px;
      z-index: 1000;
    }
    
    @media (max-width: 1024px) {
      #side-panel {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1001;
        transform: translateX(100%);
      }
      #side-panel.is-open {
        transform: translateX(0);
      }
      #panel-toggle-btn {
        display: block; /* Show toggle button on mobile */
      }
    }
  </style>
{% endblock %}

{% block content %}
  <div id="app-container">
    <div id="map-container">
      <div id="map"></div>
      <div id="map-overlay"></div>
      <button id="panel-toggle-btn" class="map-btn" title="Show list"><i class="fa-solid fa-list"></i></button>
    </div>
    <div id="side-panel">
      <div id="controls-container">
        <div class="search-wrapper">
          <input type="text" id="place-search" placeholder="Search for a place..." />
          <i class="fa-solid fa-search search-icon"></i>
        </div>
        <div class="toggle-switch">
          <label for="radius-toggle">Show Place Radii</label>
          <label class="switch">
            <input type="checkbox" id="radius-toggle" />
            <span class="slider"></span>
          </label>
        </div>
      </div>
      <div id="place-list"></div>
    </div>
  </div>

  <div id="map-controls">
    <button id="fit-bounds-btn" class="map-btn" title="Fit to all places"><i class="fa-solid fa-expand"></i></button>
    <button id="current-location-btn" class="map-btn" title="Go to my location"><i class="fa-solid fa-location-crosshairs"></i></button>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Leaflet.js JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

  <script type="text/javascript">
    // Set active navigation item
    document.addEventListener('DOMContentLoaded', function () {
      const placesLink = document.querySelector('a[href="/manage/locations/places-map/"]')
      if (placesLink) {
        placesLink.classList.add('active')
      }
    })
    
    const map = L.map('map').setView([40.7128, -74.006], 12) // Default to NYC
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(map)
    
    let placesLayer, currentLocationMarker
    let placeRadii = []
    const sidePanel = document.getElementById('side-panel')
    const panelToggleBtn = document.getElementById('panel-toggle-btn')
    const mapOverlay = document.getElementById('map-overlay')
    const placeListContainer = document.getElementById('place-list')
    const radiusToggle = document.getElementById('radius-toggle')
    const placeSearch = document.getElementById('place-search')
    
    // --- Custom Icons ---
    const placeIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    })
    
    const currentLocationIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    })
    
    // --- Helper Functions ---
    function updatePlaceList(places) {
      placeListContainer.innerHTML = ''
      if (places && places.length > 0) {
        places.forEach((place) => {
          const placeItem = document.createElement('div')
          placeItem.className = 'place-item'
          placeItem.innerHTML = `
                          <div class="place-name">${place.name}</div>
                          <div class="place-address">${place.address || 'No address'}</div>
                          <div class="place-distance">${place.distance ? (place.distance * 0.000621371).toFixed(2) + ' mi' : ''}</div>
                        `
          placeItem.addEventListener('click', () => {
            // Zoom to place on map
            map.setView([place.coordinates.coordinates[1], place.coordinates.coordinates[0]], 16)
    
            // Find and open popup for this place
            if (placesLayer) {
              placesLayer.eachLayer((layer) => {
                if (layer.feature && layer.feature.properties.name === place.name) {
                  layer.openPopup()
                }
              })
            }
          })
          placeListContainer.appendChild(placeItem)
        })
      } else {
        placeListContainer.innerHTML = '<div class="place-item">No places found.</div>'
      }
    }
    
    function fetchAndDisplayPlaces() {
      const bounds = map.getBounds()
      const center = map.getCenter()
      const payload = {
        lat: center.lat,
        lng: center.lng,
        unit: 1,
        bounds: {
          ne_lat: bounds.getNorthEast().lat,
          ne_lng: bounds.getNorthEast().lng,
          sw_lat: bounds.getSouthWest().lat,
          sw_lng: bounds.getSouthWest().lng
        }
      }
    
      fetch('/api/places/search/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'X-CSRFToken': '{{ csrf_token }}' },
        body: JSON.stringify(payload)
      })
        .then((response) => response.json())
        .then((data) => {
          // Clear existing places layer
          if (placesLayer) {
            map.removeLayer(placesLayer)
          }
    
          // Clear existing radii
          placeRadii.forEach((circle) => map.removeLayer(circle))
          placeRadii = []
    
          // Create new places layer
          const places = data.results || data
          placesLayer = L.geoJSON(
            places.map((place) => ({
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [place.location.lng, place.location.lat]
              },
              properties: {
                name: place.name,
                address: place.address,
                distance: place.distance,
                radius: place.radius
              }
            })),
            {
              pointToLayer: (feature, latlng) => {
                const marker = L.marker(latlng, { icon: placeIcon })
                marker.bindPopup(`
                              <strong>${feature.properties.name}</strong><br>
                              ${feature.properties.address || 'No address'}<br>
                              ${feature.properties.distance ? (feature.properties.distance * 0.000621371).toFixed(2) + ' mi away' : ''}
                            `)
    
                // Add radius circle if place has radius
                if (feature.properties.radius && feature.properties.radius > 0) {
                  const circle = L.circle(latlng, {
                    radius: feature.properties.radius,
                    fillColor: 'blue',
                    color: 'blue',
                    weight: 2,
                    opacity: 0.5,
                    fillOpacity: 0.1
                  })
                  placeRadii.push(circle)
                }
    
                return marker
              }
            }
          )
          placesLayer.addTo(map)
          updateRadiiVisibility()
          updatePlaceList(places)
        })
        .catch((error) => console.error('Error fetching places:', error))
    }
    
    function updateRadiiVisibility() {
      const showRadii = radiusToggle.checked
      placeRadii.forEach((circle) => {
        if (showRadii) {
          if (!map.hasLayer(circle)) map.addLayer(circle)
        } else {
          if (map.hasLayer(circle)) map.removeLayer(circle)
        }
      })
    }
    
    function togglePanel(isOpen) {
      if (isOpen) {
        sidePanel.classList.add('is-open')
        mapOverlay.style.display = 'block'
      } else {
        sidePanel.classList.remove('is-open')
        mapOverlay.style.display = 'none'
      }
    }
    
    function getCurrentLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const lat = position.coords.latitude
            const lng = position.coords.longitude
    
            // Remove existing current location marker
            if (currentLocationMarker) {
              map.removeLayer(currentLocationMarker)
            }
    
            // Add new current location marker
            currentLocationMarker = L.marker([lat, lng], { icon: currentLocationIcon }).addTo(map).bindPopup('Your current location').openPopup()
    
            // Center map on current location
            map.setView([lat, lng], 15)
          },
          (error) => {
            console.error('Error getting location:', error)
            alert('Unable to get your current location. Please check your browser settings.')
          }
        )
      } else {
        alert('Geolocation is not supported by this browser.')
      }
    }
    
    // --- Search functionality ---
    let searchTimeout
    function handlePlaceSearch() {
      const query = placeSearch.value.trim()
      if (query.length < 2) {
        return
      }
    
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        fetch(`/api/places/autocomplete/?q=${encodeURIComponent(query)}`)
          .then((response) => response.json())
          .then((data) => {
            if (data.results && data.results.length > 0) {
              const place = data.results[0]
              // Zoom to the found place
              map.setView([place.coordinates.coordinates[1], place.coordinates.coordinates[0]], 16)
              // Refresh places in the new area
              fetchAndDisplayPlaces()
            }
          })
          .catch((error) => console.error('Error searching places:', error))
      }, 500)
    }
    
    // --- Initial Load ---
    // Try to get user's current location and center map there
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          map.setView([lat, lng], 12)
          fetchAndDisplayPlaces()
        },
        (error) => {
          console.log('Could not get current location, using default')
          fetchAndDisplayPlaces()
        }
      )
    } else {
      fetchAndDisplayPlaces()
    }
    
    // --- Event Listeners ---
    map.on('moveend', fetchAndDisplayPlaces)
    radiusToggle.addEventListener('change', updateRadiiVisibility)
    panelToggleBtn.addEventListener('click', () => togglePanel(!sidePanel.classList.contains('is-open')))
    mapOverlay.addEventListener('click', () => togglePanel(false))
    placeSearch.addEventListener('input', handlePlaceSearch)
    
    document.getElementById('fit-bounds-btn').addEventListener('click', () => {
      if (placesLayer && placesLayer.getLayers().length > 0) {
        map.fitBounds(placesLayer.getBounds(), { padding: [20, 20] })
      }
    })
    
    document.getElementById('current-location-btn').addEventListener('click', getCurrentLocation)
  </script>
{% endblock %}
