{% extends 'base.html' %}

{% block title %}
  Places Map - GTX
{% endblock %}

{% block extra_css %}
  <!-- Leaflet.js CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" xintegrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <!-- Material Icons for place types -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />

  <style>
    /* Override base template styles for full-screen map */
    body {
      display: flex;
      flex-direction: column;
      height: 100vh;
      margin: 0;
      overflow: hidden;
    }
    
    .navbar-custom {
      height: auto;
    }
    
    .main-content {
      padding: 0 !important;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0; /* Important for flex child to shrink */
    }
    
    .container-fluid {
      padding: 0 !important;
      max-width: none !important;
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
    
    #app-container {
      display: flex;
      flex: 1;
      min-height: 0;
      width: 100%;
    }
    #map-container {
      flex-grow: 1;
      height: 100%;
      position: relative;
    }
    #map {
      height: 100%;
      width: 100%;
    }
    #side-panel {
      width: 300px;
      min-width: 200px;
      height: 100%;
      background-color: #f8f9fa;
      border-left: 1px solid #dee2e6;
      display: flex;
      flex-direction: column;
      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease-in-out;
    }
    #controls-container {
      padding: 15px;
      border-bottom: 1px solid #dee2e6;
      background-color: white;
    }
    
    .search-wrapper {
      position: relative;
      margin-bottom: 15px;
    }
    
    .search-wrapper input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .search-wrapper input:focus {
      border-color: #86b7fe;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .search-wrapper .search-icon {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      pointer-events: none;
    }
    
    .autocomplete-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #ced4da;
      border-top: none;
      border-radius: 0 0 4px 4px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1001;
      display: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .autocomplete-item {
      padding: 10px 12px;
      cursor: pointer;
      border-bottom: 1px solid #f8f9fa;
      transition: background-color 0.2s;
    }
    
    .autocomplete-item:hover {
      background-color: #f8f9fa;
    }
    
    .autocomplete-item:last-child {
      border-bottom: none;
    }
    
    .autocomplete-item-name {
      font-weight: 500;
      color: #212529;
      margin-bottom: 2px;
    }
    
    .autocomplete-item-address {
      font-size: 12px;
      color: #6c757d;
    }
    
    .toggle-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: opacity 0.3s ease, max-height 0.3s ease, margin 0.3s ease, padding 0.3s ease;
      overflow: hidden;
      max-height: 50px;
      padding: 10px 0;
    }
    
    .toggle-switch.hidden {
      opacity: 0;
      max-height: 0;
      margin-bottom: 0;
      padding: 0;
    }
    .toggle-switch label {
      color: #495057;
    }
    .toggle-switch .switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 22px;
    }
    .toggle-switch .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: 0.4s;
      border-radius: 22px;
    }
    .slider:before {
      position: absolute;
      content: '';
      height: 18px;
      width: 18px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: 0.4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #0d6efd;
    }
    input:checked + .slider:before {
      transform: translateX(18px);
    }
    
    #place-list {
      flex-grow: 1;
      overflow-y: auto;
      padding: 0;
      margin: 0;
    }
    .place-item {
      padding: 10px 15px;
      border-bottom: 1px solid #dee2e6;
      cursor: pointer;
      transition: background-color 0.2s;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    .place-item:hover {
      background-color: #e9ecef;
    }
    .place-item:last-child {
      border-bottom: none;
    }
    .place-cover-image {
      width: 50px;
      height: 50px;
      object-fit: cover;
      border-radius: 6px;
      flex-shrink: 0;
    }
    .place-content {
      flex: 1;
      min-width: 0;
    }
    .place-name {
      font-weight: 600;
      color: #212529;
      margin-bottom: 4px;
    }
    .place-address {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 4px;
    }
    .place-distance {
      font-size: 11px;
      color: #868e96;
    }
    .place-type {
      font-size: 11px;
      color: #6c757d;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .place-type .material-icons {
      font-size: 12px;
      width: 14px;
      height: 14px;
      line-height: 14px;
      text-align: center;
    }
    
    #map-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 1000;
      display: none;
    }
    
    .map-btn {
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 8px;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      transition: background-color 0.2s;
    }
    .map-btn:hover {
      background-color: #f8f9fa;
    }
    
    #panel-toggle-btn {
      top: 10px;
      right: 10px;
      display: none;
    }
    
    #map-controls {
      position: absolute;
      bottom: 10px;
      left: 10px;
      display: flex;
      flex-direction: column-reverse;
      gap: 5px;
      z-index: 1000;
    }
    
    /* Place Details Modal */
    .place-details-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 400px;
      bottom: 0;
      background: white;
      z-index: 2000;
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;
      box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .place-details-modal.is-open {
      transform: translateX(0);
    }
    
    .place-details-content {
      height: 100%;
      overflow-y: auto;
      position: relative;
    }
    
    .close-btn {
      position: absolute;
      top: 15px;
      left: 15px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      z-index: 2001;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: background-color 0.2s;
    }
    
    .close-btn:hover {
      background: rgba(0, 0, 0, 0.9);
    }
    
    .place-details-header {
      position: relative;
      height: 200px;
      overflow: hidden;
    }
    
    .place-logo-container {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .place-logo {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
    
    .place-details-body {
      padding: 30px;
    }
    
    .place-main-info {
      margin-bottom: 30px;
    }
    
    .place-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #212529;
      margin: 0 0 15px 0;
      line-height: 1.2;
    }
    
    .place-type-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 10px;
      font-size: 16px;
      color: #6c757d;
    }
    
    .place-type-info .material-icons {
      font-size: 18px;
    }
    
    .place-address-info {
      font-size: 16px;
      color: #6c757d;
      margin-bottom: 10px;
    }
    
    .place-description {
      font-size: 16px;
      line-height: 1.6;
      color: #495057;
      margin-bottom: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #007bff;
    }
    
    .place-tags-section,
    .place-photos-section,
    .place-additional-info {
      margin-bottom: 30px;
    }
    
    .place-tags-section h3,
    .place-photos-section h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #212529;
      margin-bottom: 15px;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 8px;
    }
    
    .place-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .place-tag {
      background: #e3f2fd;
      color: #1976d2;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      border: 1px solid #bbdefb;
    }
    
    .place-photos {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 15px;
    }
    
    .place-photo {
      aspect-ratio: 1;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .place-photo img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    
    .info-item h4 {
      font-size: 1.2rem;
      font-weight: 600;
      color: #212529;
      margin-bottom: 10px;
    }
    
    .info-content {
      font-size: 14px;
      color: #6c757d;
      line-height: 1.5;
    }
    
    @media (max-width: 1024px) {
      .place-details-modal {
        right: 0;
        left: 0;
      }
    
      .place-details-body {
        padding: 20px;
      }
    
      .place-title {
        font-size: 2rem;
      }
    
      .info-grid {
        grid-template-columns: 1fr;
      }
    
      #side-panel {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1001;
        transform: translateX(100%);
      }
      #side-panel.is-open {
        transform: translateX(0);
      }
      #panel-toggle-btn {
        display: block; /* Show toggle button on mobile */
      }
    }
  </style>
{% endblock %}

{% block content %}
  <div id="app-container">
    <div id="map-container">
      <div id="map"></div>
      <div id="map-overlay"></div>
      <button id="panel-toggle-btn" class="map-btn" title="Show list"><i class="fa-solid fa-list"></i></button>
    </div>
    <div id="side-panel">
      <div id="controls-container">
        <div class="search-wrapper">
          <input type="text" id="place-search" placeholder="Search for a place..." />
          <i class="fa-solid fa-search search-icon"></i>
          <div id="autocomplete-dropdown" class="autocomplete-dropdown"></div>
        </div>
        <div class="toggle-switch">
          <label for="radius-toggle">Show Place Radii</label>
          <label class="switch">
            <input type="checkbox" id="radius-toggle" />
            <span class="slider"></span>
          </label>
        </div>
      </div>
      <div id="place-list"></div>
    </div>
  </div>

  <div id="map-controls">
    <button id="fit-bounds-btn" class="map-btn" title="Fit to all places"><i class="fa-solid fa-expand"></i></button>
    <button id="current-location-btn" class="map-btn" title="Go to my location"><i class="fa-solid fa-location-crosshairs"></i></button>
  </div>

  <!-- Place Details Modal -->
  <div id="place-details-modal" class="place-details-modal">
    <div class="place-details-content">
      <button id="close-place-details" class="close-btn" title="Close"><i class="fa-solid fa-times"></i></button>

      <div class="place-details-header">
        <div id="place-logo-container" class="place-logo-container">
          <img id="place-logo" class="place-logo" alt="Place logo" />
        </div>
      </div>

      <div class="place-details-body">
        <div class="place-main-info">
          <h1 id="place-title" class="place-title"></h1>
          <div id="place-type-info" class="place-type-info"></div>
          <div id="place-address-info" class="place-address-info"></div>
        </div>

        <div id="place-description" class="place-description"></div>

        <div id="place-tags-section" class="place-tags-section">
          <h3>Tags</h3>
          <div id="place-tags" class="place-tags"></div>
        </div>

        <div id="place-photos-section" class="place-photos-section">
          <h3>Photos</h3>
          <div id="place-photos" class="place-photos"></div>
        </div>

        <div id="place-additional-info" class="place-additional-info">
          <div class="info-grid">
            <div id="place-hours" class="info-item">
              <h4>Hours</h4>
              <div class="info-content"></div>
            </div>
            <div id="place-contact" class="info-item">
              <h4>Contact</h4>
              <div class="info-content"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Leaflet.js JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

  <script type="text/javascript">
    // Set active navigation item
    document.addEventListener('DOMContentLoaded', function () {
      const placesLink = document.querySelector('a[href="/manage/locations/places-map/"]')
      if (placesLink) {
        placesLink.classList.add('active')
      }
    })
    
    const map = L.map('map').setView([40.7128, -74.006], 12) // Default to NYC
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(map)
    
    let placesLayer, currentLocationMarker
    let placeRadii = []
    const sidePanel = document.getElementById('side-panel')
    const panelToggleBtn = document.getElementById('panel-toggle-btn')
    const mapOverlay = document.getElementById('map-overlay')
    const placeListContainer = document.getElementById('place-list')
    const radiusToggle = document.getElementById('radius-toggle')
    const placeSearch = document.getElementById('place-search')
    const autocompleteDropdown = document.getElementById('autocomplete-dropdown')
    const placeDetailsModal = document.getElementById('place-details-modal')
    const closePlaceDetailsBtn = document.getElementById('close-place-details')
    
    // --- Custom Icons ---
    const placeIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    })
    
    const currentLocationIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    })
    
    // --- Helper Functions ---
    function updatePlaceList(places) {
      placeListContainer.innerHTML = ''
      if (places && places.length > 0) {
        places.forEach((place) => {
          const placeItem = document.createElement('div')
          placeItem.className = 'place-item'
    
          // Build HTML content
          let htmlContent = ''
    
          // Add logo if exists
          if (place.logo) {
            htmlContent += '<img src="' + place.logo.url + '" alt="' + place.name + '" class="place-cover-image">'
          }
    
          // Start place content div
          htmlContent += '<div class="place-content">'
          htmlContent += '<div class="place-name">' + place.name + '</div>'
          htmlContent += '<div class="place-address">' + (place.address || 'No address') + '</div>'
    
          // Add place type if exists
          if (place.primary_type) {
            htmlContent += '<div class="place-type">'
            if (place.primary_type.material_icon) {
              htmlContent += '<i class="material-icons">' + place.primary_type.material_icon + '</i>'
            }
            htmlContent += place.primary_type.name
            htmlContent += '</div>'
          }
    
          // Close place content div
          htmlContent += '</div>'
    
          placeItem.innerHTML = htmlContent
          placeItem.addEventListener('click', () => {
            // Zoom to place on map
            map.setView([place.location.lat, place.location.lng], 16)
    
            // Find and open popup for this place
            if (placesLayer) {
              placesLayer.eachLayer((layer) => {
                if (layer.feature && layer.feature.properties.name === place.name) {
                  layer.openPopup()
                }
              })
            }
          })
          placeListContainer.appendChild(placeItem)
        })
      } else {
        placeListContainer.innerHTML = '<div class="place-item">No places found.</div>'
      }
    }
    
    function fetchAndDisplayPlaces() {
      const bounds = map.getBounds()
      // Use the user's current location as the center if available
      const center = currentLocationMarker ? [currentLocationMarker.getLatLng().lat, currentLocationMarker.getLatLng().lng] : map.getCenter()
      const payload = {
        lat: center.lat ?? 40.7128,
        lng: center.lng ?? -74.006,
        unit: 1,
        bounds: {
          ne_lat: bounds.getNorthEast().lat,
          ne_lng: bounds.getNorthEast().lng,
          sw_lat: bounds.getSouthWest().lat,
          sw_lng: bounds.getSouthWest().lng
        }
      }
    
      fetch('/api/places/search/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'X-CSRFToken': '{{ csrf_token }}' },
        body: JSON.stringify(payload)
      })
        .then((response) => response.json())
        .then((data) => {
          // Clear existing places layer
          if (placesLayer) {
            map.removeLayer(placesLayer)
          }
    
          // Clear existing radii
          placeRadii.forEach((circle) => map.removeLayer(circle))
          placeRadii = []
    
          // Create new places layer
          const places = data.results || data
          placesLayer = L.geoJSON(
            places.map((place) => ({
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [place.location.lng, place.location.lat]
              },
              properties: {
                id: place.id,
                name: place.name,
                address: place.address,
                distance: place.distance,
                radius: place.radius,
                place_type: place.primary_type,
                place_type_icon: place.primary_type?.material_icon
              }
            })),
            {
              pointToLayer: (feature, latlng) => {
                const marker = L.marker(latlng, { icon: placeIcon })
                // Create place type HTML for popup
                let popupPlaceTypeHtml = ''
                if (feature.properties.place_type && feature.properties.place_type.name) {
                  const popupIconHtml = feature.properties.place_type_icon ? '<i class="material-icons" style="font-size: 12px; vertical-align: middle;">' + feature.properties.place_type_icon + '</i> ' : ''
                  popupPlaceTypeHtml = '<div style="margin: 5px 0; color: #6c757d; font-size: 12px;">' + popupIconHtml + feature.properties.place_type.name + '</div>'
                }
    
                marker.bindPopup('<strong>' + feature.properties.name + '</strong><br>' + (feature.properties.address || 'No address') + '<br>' + popupPlaceTypeHtml + '<button onclick="showPlaceDetails(\'' + feature.properties.id + '\')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-right: 5px;">View Place</button>' + '<a href="/manage/locations/places/' + feature.properties.id + '/edit/" style="color: #007bff; text-decoration: none;">Edit Place</a>')
    
                // Add radius circle if place has radius
                if (feature.properties.radius && feature.properties.radius > 0) {
                  const circle = L.circle(latlng, {
                    radius: feature.properties.radius,
                    fillColor: 'blue',
                    color: 'blue',
                    weight: 2,
                    opacity: 0.5,
                    fillOpacity: 0.1
                  })
                  placeRadii.push(circle)
                }
    
                return marker
              }
            }
          )
          placesLayer.addTo(map)
          updateRadiiVisibility()
          updatePlaceList(places)
        })
        .catch((error) => console.error('Error fetching places:', error))
    }
    
    function updateRadiiVisibility() {
      const showRadii = radiusToggle.checked
      placeRadii.forEach((circle) => {
        if (showRadii) {
          if (!map.hasLayer(circle)) map.addLayer(circle)
        } else {
          if (map.hasLayer(circle)) map.removeLayer(circle)
        }
      })
    }
    
    function togglePanel(isOpen) {
      if (isOpen) {
        sidePanel.classList.add('is-open')
        mapOverlay.style.display = 'block'
      } else {
        sidePanel.classList.remove('is-open')
        mapOverlay.style.display = 'none'
      }
    }
    
    function getCurrentLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const lat = position.coords.latitude
            const lng = position.coords.longitude
    
            // Remove existing current location marker
            if (currentLocationMarker) {
              map.removeLayer(currentLocationMarker)
            }
    
            // Add new current location marker
            currentLocationMarker = L.marker([lat, lng], { icon: currentLocationIcon }).addTo(map).bindPopup('Your current location').openPopup()
    
            // Center map on current location
            map.setView([lat, lng], 15)
          },
          (error) => {
            console.error('Error getting location:', error)
            alert('Unable to get your current location. Please check your browser settings.')
          }
        )
      } else {
        alert('Geolocation is not supported by this browser.')
      }
    }
    
    // --- Place Details Modal Functions ---
    function showPlaceDetails(placeId) {
      // Fetch place details from API
      fetch(`/api/places/${placeId}/`)
        .then((response) => response.json())
        .then((place) => {
          populatePlaceDetails(place)
          placeDetailsModal.classList.add('is-open')
        })
        .catch((error) => {
          console.error('Error fetching place details:', error)
        })
    }
    
    function hidePlaceDetails() {
      placeDetailsModal.classList.remove('is-open')
    }
    
    function populatePlaceDetails(place) {
      // Set logo
      const placeLogo = document.getElementById('place-logo')
      const placeLogoContainer = document.getElementById('place-logo-container')
      if (place.logo && place.logo.url) {
        placeLogo.src = place.logo.url
        placeLogo.style.display = 'block'
      } else {
        placeLogo.style.display = 'none'
      }
    
      // Set title
      document.getElementById('place-title').textContent = place.name || 'Unnamed Place'
    
      // Set place type
      const placeTypeInfo = document.getElementById('place-type-info')
      if (place.primary_type) {
        const iconHtml = place.primary_type.material_icon ? '<i class="material-icons">' + place.primary_type.material_icon + '</i>' : ''
        placeTypeInfo.innerHTML = iconHtml + place.primary_type.name
      } else {
        placeTypeInfo.innerHTML = ''
      }
    
      // Set address
      document.getElementById('place-address-info').textContent = place.address || 'No address available'
    
      // Set description
      const description = place.summary || place.description || 'No description available'
      document.getElementById('place-description').textContent = description
    
      // Set tags
      const tagsContainer = document.getElementById('place-tags')
      tagsContainer.innerHTML = ''
      if (place.tags && place.tags.length > 0) {
        place.tags.forEach((tag) => {
          const tagElement = document.createElement('span')
          tagElement.className = 'place-tag'
          tagElement.textContent = tag.name
          tagsContainer.appendChild(tagElement)
        })
        document.getElementById('place-tags-section').style.display = 'block'
      } else {
        document.getElementById('place-tags-section').style.display = 'none'
      }
    
      // Set photos (placeholder for now)
      const photosContainer = document.getElementById('place-photos')
      photosContainer.innerHTML = '<p style="color: #6c757d; font-style: italic;">Photos coming soon...</p>'
    
      // Set contact info
      const contactInfo = document.getElementById('place-contact').querySelector('.info-content')
      let contactHtml = ''
      if (place.phone) contactHtml += '<div><strong>Phone:</strong> ' + place.phone + '</div>'
      if (place.website) contactHtml += '<div><strong>Website:</strong> <a href="' + place.website + '" target="_blank">' + place.website + '</a></div>'
      if (place.email) contactHtml += '<div><strong>Email:</strong> <a href="mailto:' + place.email + '">' + place.email + '</a></div>'
      contactInfo.innerHTML = contactHtml || 'No contact information available'
    
      // Set hours info (placeholder)
      const hoursInfo = document.getElementById('place-hours').querySelector('.info-content')
      if (place.opening_time || place.closing_time) {
        let hoursHtml = ''
        if (place.opening_time) hoursHtml += '<div><strong>Opens:</strong> ' + new Date(place.opening_time).toLocaleTimeString() + '</div>'
        if (place.closing_time) hoursHtml += '<div><strong>Closes:</strong> ' + new Date(place.closing_time).toLocaleTimeString() + '</div>'
        hoursInfo.innerHTML = hoursHtml
      } else {
        hoursInfo.innerHTML = 'Hours not available'
      }
    }
    
    // --- Search functionality ---
    let searchTimeout
    
    function showAutocompleteResults(results) {
      autocompleteDropdown.innerHTML = ''
    
      if (results && results.length > 0) {
        results.forEach((place) => {
          const item = document.createElement('div')
          item.className = 'autocomplete-item'
          item.innerHTML = `
                                                                    <div class="autocomplete-item-name">${place.name}</div>
                                                                    <div class="autocomplete-item-address">${place.address || 'No address'}</div>
                                                                  `
    
          item.addEventListener('click', () => {
            // Set the input value to the selected place name
            placeSearch.value = place.name
            // Hide the dropdown
            hideAutocompleteResults()
            // Zoom to the selected place
            map.setView([place.location.lat, place.location.lng], 16)
            // Refresh places in the new area
            fetchAndDisplayPlaces()
          })
    
          autocompleteDropdown.appendChild(item)
        })
    
        autocompleteDropdown.style.display = 'block'
      } else {
        hideAutocompleteResults()
      }
    }
    
    function hideAutocompleteResults() {
      autocompleteDropdown.style.display = 'none'
    }
    
    function handlePlaceSearch() {
      const query = placeSearch.value.trim()
    
      if (query.length < 3) {
        hideAutocompleteResults()
        return
      }
    
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        fetch(`/api/places/autocomplete/?q=${encodeURIComponent(query)}`)
          .then((response) => response.json())
          .then((data) => {
            showAutocompleteResults(data.results || data)
          })
          .catch((error) => {
            console.error('Error searching places:', error)
            hideAutocompleteResults()
          })
      }, 200)
    }
    
    // --- Initial Load ---
    // Try to get user's current location and center map there
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          map.setView([lat, lng], 12)
          fetchAndDisplayPlaces()
        },
        (error) => {
          console.log('Could not get current location, using default')
          fetchAndDisplayPlaces()
        }
      )
    } else {
      fetchAndDisplayPlaces()
    }
    
    // Make showPlaceDetails globally accessible
    window.showPlaceDetails = showPlaceDetails
    
    // --- Event Listeners ---
    map.on('moveend', fetchAndDisplayPlaces)
    radiusToggle.addEventListener('change', updateRadiiVisibility)
    panelToggleBtn.addEventListener('click', () => togglePanel(!sidePanel.classList.contains('is-open')))
    mapOverlay.addEventListener('click', () => togglePanel(false))
    placeSearch.addEventListener('input', handlePlaceSearch)
    closePlaceDetailsBtn.addEventListener('click', hidePlaceDetails)
    
    // Hide autocomplete when clicking outside
    document.addEventListener('click', (e) => {
      if (!placeSearch.contains(e.target) && !autocompleteDropdown.contains(e.target)) {
        hideAutocompleteResults()
      }
    })
    
    // Hide autocomplete when pressing Escape
    placeSearch.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        hideAutocompleteResults()
        placeSearch.blur()
      }
    })
    
    // Close place details modal with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && placeDetailsModal.classList.contains('is-open')) {
        hidePlaceDetails()
      }
    })
    
    document.getElementById('fit-bounds-btn').addEventListener('click', () => {
      if (placesLayer && placesLayer.getLayers().length > 0) {
        map.fitBounds(placesLayer.getBounds(), { padding: [20, 20] })
      }
    })
    
    document.getElementById('current-location-btn').addEventListener('click', getCurrentLocation)
  </script>
{% endblock %}
