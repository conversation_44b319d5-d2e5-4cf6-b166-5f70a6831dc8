from django.contrib import admin


from .models import Place, PlaceHours, PlacePhoto, PlaceType, PlaceVisit
from django.contrib.gis.admin import GISModelAdmin


class PlaceHoursAdmin(admin.ModelAdmin):
    list_display = ["place", "__str__"]
    search_fields = ["place__name"]
    ordering = ["place__name", "open_day"]


class PlaceHoursInline(admin.TabularInline):
    model = PlaceHours


class PlaceAdmin(GISModelAdmin):
    list_per_page = 25
    ordering = ["name"]
    inlines = [PlaceHoursInline]
    list_display = ["name", "hash_id", "primary_type", "address",
                    "timezone", "approved", "browsable",  "explorable", "should_fetch_busyness_forecast"]
    list_editable = ["approved", "primary_type", "browsable", "explorable",
                     "should_fetch_busyness_forecast", "timezone"]
    readonly_fields = ["hash_id", "created", "updated"]
    search_fields = ["name", "address"]
    default_lon = -77.0365  # Default is 0.0
    default_lat = 38.8977   # Default is 0.0
    list_filter = ["primary_type", "approved", "browsable", "explorable",
                   "featured", "should_fetch_busyness_forecast", "created", "radius"]
    autocomplete_fields = ["primary_type",
                           "secondary_types", "tags", "manager"]
    actions = ["make_browsable", "make_unbrowsable",
               "make_featured", "make_unfeatured", "make_explorable", "hide_from_explore", "approve", "should_fetch_busyness"]
    readonly_fields = ["hash_id"]

    def make_browsable(self, request, queryset):
        queryset.update(browsable=True)

    def make_unbrowsable(self, request, queryset):
        queryset.update(browsable=False)

    def make_featured(self, request, queryset):
        queryset.update(featured=True)

    def make_unfeatured(self, request, queryset):
        queryset.update(featured=False)

    def approve(self, request, queryset):
        queryset.update(approved=True)

    def make_explorable(self, request, queryset):
        queryset.update(explorable=True)

    def hide_from_explore(self, request, queryset):
        queryset.update(explorable=False)

    def should_fetch_busyness(self, request, queryset):
        queryset.update(should_fetch_busyness_forecast=True)


class PlaceTypeAdmin(admin.ModelAdmin):
    list_display = ["name", "material_icon",
                    "image", "fetch_busyness_forecast"]
    ordering = ["name"]
    search_fields = ["name"]
    actions = ["make_fetch_busyness_forecast",
               "make_not_fetch_busyness_forecast"]

    def make_fetch_busyness_forecast(self, request, queryset):
        queryset.update(fetch_busyness_forecast=True)

    def make_not_fetch_busyness_forecast(self, request, queryset):
        queryset.update(fetch_busyness_forecast=False)


class PlacePhotoAdmin(admin.ModelAdmin):
    list_display = ["id", "url", "place",
                    "approved", "uploaded_by", "uploaded"]
    readonly_fields = ["uploaded"]
    list_filter = ["uploaded", "place"]
    ordering = ["uploaded"]
    actions = ['approve', 'reject']

    def approve(self, request, queryset):
        queryset.update(approved=True)
    approve.short_description = "Approve selected photos"

    def reject(self, request, queryset):
        queryset.update(approved=False)
    reject.short_description = "Reject selected photos"


class PlaceVisitAdmin(GISModelAdmin):
    list_display = ["place", "profile", "arrival_time",
                    "departure_time",  "recorded_distance", "passive_record", "valid", "timestamp",]
    readonly_fields = ["timestamp"]
    list_filter = ["arrival_time", "timestamp",
                   "valid", "passive_record", "device_id"]
    ordering = ["-arrival_time"]


admin.site.register(Place, PlaceAdmin)
admin.site.register(PlaceHours, PlaceHoursAdmin)
admin.site.register(PlaceType, PlaceTypeAdmin)
admin.site.register(PlaceVisit, PlaceVisitAdmin)
admin.site.register(PlacePhoto, PlacePhotoAdmin)
