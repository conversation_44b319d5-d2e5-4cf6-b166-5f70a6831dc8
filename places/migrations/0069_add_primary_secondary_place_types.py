# Generated by Django 5.1.6 on 2025-09-11 15:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('places', '0068_place_dislike_summary_place_last_content_fetched_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='place',
            name='primary_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_places', to='places.placetype'),
        ),
        migrations.AddField(
            model_name='place',
            name='secondary_types',
            field=models.ManyToManyField(blank=True, related_name='secondary_places', to='places.placetype'),
        ),
    ]
