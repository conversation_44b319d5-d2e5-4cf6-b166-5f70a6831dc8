# Generated by Django 5.1.6 on 2025-09-11 15:55

from django.db import migrations


class Migration(migrations.Migration):

    def forwards_func(apps, schema_editor):
        """
        Migrates data from the old 'type' M2M field to the new 'primary_type'
        and 'secondary_types' fields.
        """
        Place = apps.get_model('places', 'Place')

        for place in Place.objects.prefetch_related('type').all():
            current_types = list(place.type.all())

            if not current_types:
                continue

            # --- THIS IS THE CORE LOGIC ---
            # Assumption: The first type associated is the primary one.
            # You can change this logic if you have a different way to determine the primary type.
            primary = current_types[0]
            secondaries = current_types[1:]

            # Assign the new fields
            place.primary_type = primary
            place.secondary_types.set(secondaries)
            place.save()
            # We don't need to call place.save() here because the ORM operations
            # within the migration will handle it.

    def reverse_func(apps, schema_editor):
        """
        We can leave this empty if we don't need to reverse the migration.
        Reversing would be complex and is often not required for data migrations.
        """
        pass

    dependencies = [
        ('places', '0069_add_primary_secondary_place_types'),
    ]

    operations = [
        migrations.RunPython(forwards_func, reverse_func),
        migrations.RemoveField(
            model_name='place',
            name='type',
        ),
    ]
