from profile import Profile
from typing import List
from venv import logger
from django.contrib.gis.geos import Point, Polygon
from django.contrib.gis.measure import D
from busy_times.services import BusyTimesServices
from guytrax.constants import MeasurementUnits
from django.contrib.gis.db.models.functions import Distance

from rate_limits.services import RateLimitException
from tags.models import Tag
from vibes.models import Vibe
from .models import Place, PlaceType
from django.db.models import Q, Value, F, CharField
from django.conf import settings
from django.utils import timezone
import datetime
from background_task import background


class LatLngBounds():
    ne_lat: float
    ne_lng: float
    sw_lat: float
    sw_lng: float

    def __init__(self, ne_lat: float, ne_lng: float, sw_lat: float, sw_lng: float):
        self.ne_lat = ne_lat
        self.ne_lng = ne_lng
        self.sw_lat = sw_lat
        self.sw_lng = sw_lng


class PlacesServices():

    MINIMUM_SIMILARITY_THRESHOLD = 25.0
    PLACE_TYPE_MISMATCH_PENALTY = 0.25

    @staticmethod
    def get_tag_similarity_score(place_a: Place, place_b: Place) -> float:
        """
        Calculates a similarity score between two places, including a penalty for mismatched place types. Assumes that place_a is the target place.
        """
        # Calculate the tag-based score
        place_a_tag_ids = place_a.tags.all().values_list('id', flat=True)
        common_tags = place_b.tags.filter(id__in=place_a_tag_ids)
        tag_score = sum(tag.weight for tag in common_tags)

        # Apply a penalty if the place types don't match
        place_a_types = place_a.type.all().values_list('name', flat=True)
        place_b_types = place_b.type.all().values_list('name', flat=True)

        if not any(t in place_b_types for t in place_a_types):
            return tag_score * PlacesServices.PLACE_TYPE_MISMATCH_PENALTY

        return tag_score

    @staticmethod
    def find_similar_places(target_place: Place, num_results: int = 10, distance: float = 5):
        """
        Finds places most similar to a target place using database-level calculations. Assumes that target_place is the place to be compared against.
        """
        from django.db.models import Sum, Case, When, FloatField, Exists, OuterRef, F, Value

        # Get target place's tag and type IDs
        target_tag_ids = list(target_place.tags.values_list('id', flat=True))
        target_type_ids = list(target_place.type.values_list('id', flat=True))

        if not target_tag_ids:
            return Place.objects.none()

        # Subquery to check if places share any types
        shared_types = Place.objects.filter(
            id=OuterRef('id'),
            type__id__in=target_type_ids
        )

        # Get the coordinates of the current place
        place_coordinates = target_place.coordinates

        similar_places = Place.objects.exclude(id=target_place.id).annotate(
            # Calculate tag similarity score
            tag_score=Sum(
                Case(
                    When(tags__id__in=target_tag_ids, then='tags__weight'),
                    default=0,
                    output_field=FloatField()
                )
            ),
            # Check if types match
            has_shared_type=Exists(shared_types),
            # Calculate distance
            distance=Distance("coordinates", place_coordinates),
            # Apply penalty for type mismatch
            final_score=Case(
                When(
                    has_shared_type=True,
                    then='tag_score'
                ),
                default=F('tag_score') *
                Value(PlacesServices.PLACE_TYPE_MISMATCH_PENALTY),
                output_field=FloatField()
            )
        ).filter(
            final_score__gt=PlacesServices.MINIMUM_SIMILARITY_THRESHOLD,
            # Filter for approved and browsable places
            approved=True,
            browsable=True,
            # Filter for places within distance miles
            coordinates__distance_lte=(
                place_coordinates, D(mi=distance), "spheroid"),
        ).order_by('-final_score')[:num_results]

        return similar_places

    @staticmethod
    def filter_places_by_vibe(query, vibe):
        inclusion_filters = Q()
        exclusion_filters = Q()

        tags = vibe.tags.values('id').all()
        types = vibe.types.values('id').all()
        excluded_types = vibe.excluded_types.values('id').all()
        excluded_tags = vibe.excluded_tags.values('id').all()

        if types:
            inclusion_filters |= Q(type__in=types)
        if tags:
            inclusion_filters |= Q(tags__in=tags)

        if excluded_types:
            exclusion_filters &= ~Q(type__in=excluded_types)
        if excluded_tags:
            exclusion_filters &= ~Q(tags__in=excluded_tags)

        # Combine inclusion and exclusion filters
        if inclusion_filters:
            query = query.filter(inclusion_filters)

        query = query.filter(exclusion_filters)

        return query

    @staticmethod
    def nearest_places(point: Point,
                       current_location: Point = None,
                       bounds: LatLngBounds = None,
                       name: str = None,
                       types: List[PlaceType] = None,
                       tags: List[Tag] = None,
                       distance: float = settings.DEFAULT_SEARCH_RADIUS,
                       unit: MeasurementUnits = MeasurementUnits.IMPERIAL,
                       count: int = 50,
                       only_browsable: bool = True,
                       serves_food: bool = None,
                       contactless_payments: bool = None,
                       accepts_cc: bool = None,
                       open_now: bool = None,
                       vibe: Vibe = None,
                       favorited: bool = None,
                       profile: Profile = None,
                       busy_now: bool = None,
                       ):
        name_search = False
        if name is not None and name != "":
            name_search = True
            query = Place.objects.filter(Q(name__icontains=name) | Q(
                address__icontains=name))
        elif bounds is not None:
            polygon = Polygon(
                ((bounds.sw_lng, bounds.sw_lat),
                 (bounds.ne_lng, bounds.sw_lat),
                    (bounds.ne_lng, bounds.ne_lat),
                    (bounds.sw_lng, bounds.ne_lat),
                    (bounds.sw_lng, bounds.sw_lat))
            )
            query = Place.objects.filter(coordinates__within=polygon)
        else:
            d: D
            if distance == 0:
                distance = settings.DEFAULT_SEARCH_RADIUS
            if unit == MeasurementUnits.IMPERIAL:
                d = D(mi=distance)
            else:
                d = D(km=distance)
            query = Place.objects
            if distance > 0:
                query = query.filter(
                    coordinates__distance_lte=(point, d, "spheroid"))

        if current_location is None:
            current_location = point

        if vibe is not None:
            query = PlacesServices.filter_places_by_vibe(query, vibe)
        elif types is not None and len(types) > 0 and tags is not None and len(tags) > 0:
            query = query.filter(Q(type__in=types) & Q(tags__in=tags))
        elif types is not None and len(types) > 0:
            query = query.filter(type__in=types)
        elif tags is not None and len(tags) > 0:
            query = query.filter(tags__in=tags)

        if serves_food is True:
            query = query.filter(serves_food=serves_food)
        if contactless_payments is True:
            query = query.filter(contactless_payments=contactless_payments)
        if accepts_cc is True:
            query = query.exclude(cash_only=True)
        if only_browsable and not name_search:
            query = query.filter(browsable=True)
        if favorited:
            query = query.filter(
                id__in=profile.favorite_places.values_list('id', flat=True))
        if busy_now:
            open_now = True
            logger.info("Filtering for busy places.")
            try:
                busy_place_ids = PlacesServices.get_currently_busy_places(threshold=50).values_list('id',
                                                                                                    flat=True)
                query = query.filter(
                    id__in=busy_place_ids)
            except Exception as e:
                logger.error(f"Error filtering for busy places: {e}")

        places = query. \
            annotate(distance=Distance("coordinates", current_location)). \
            filter(closed=False, approved=True). \
            distinct(). \
            order_by("distance"). \
            select_related(
                'besttime_info',
                'besttime_info__live_status'
            ). \
            prefetch_related(
                "primary_type",
                "hours",
                "tags",
                'besttime_info__daily_forecasts'
            )[:count]

        if open_now:
            places = [p for p in places if p.is_open()]

        return places

    @staticmethod
    def fuzzy_search(name: str, count: int = 10):
        import re
        name = name.strip().replace(".", "")
        address_pattern = r'\d+\s+[\w\s]+(?:street|st|avenue|ave|road|rd|highway|hwy|square|sq|trail|trl|drive|dr|court|ct|park|parkway|pkwy|circle|cir|boulevard|blvd)\W?'
        address_match = re.search(address_pattern, name, re.IGNORECASE)
        if address_match:
            address = address_match.group(0).strip()
            print("Found address: ", address)
            return Place.objects.filter(address__icontains=address, approved=True).distinct().order_by("name").prefetch_related("primary_type", "hours", "tags")[:count]
        return Place.objects.annotate(querystring=Value(name, output_field=CharField()))\
            .filter(Q(querystring__icontains=F('name')) | Q(name__icontains=name) | Q(address__icontains=name)).distinct().order_by("name").prefetch_related("primary_type", "hours", "tags")[:count]

    @staticmethod
    def get_busy_places_on_day_hour(day_int: int, hour_index: int, threshold: int = 60):
        """
        Finds places that are forecasted to be busy on a specific day and hour.
        Assumes database support for JSONField array index querying (e.g., PostgreSQL).

        Args:
            day_int (int): Day of the week (0=Monday, 6=Sunday).
            hour_index (int): Hour of the day (0-23).
            threshold (int): Busyness percentage (0-100) to be considered "busy".

        Returns:
            QuerySet[Place]: A queryset of Place objects.
        """
        from django.db.models import Q
        from busy_times.models import BestTimeDailyForecast

        # Query using hour_analysis_data JSONField
        busy_forecasts = BestTimeDailyForecast.objects.filter(
            venue_info__bt_forecast_available=True,
            day_int=day_int,
        ).extra(
            where=["""
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(hour_analysis_data) AS elem
                    WHERE (elem->>'hour')::int = %s 
                    AND (elem->>'intensity_nr')::int >= %s
                )
            """],
            params=[hour_index, threshold]
        )

        # Get the place IDs from these forecasts
        busy_place_ids = busy_forecasts.values_list(
            'venue_info__place_id', flat=True)

        # Return the places
        busy_places = Place.objects.filter(
            id__in=busy_place_ids
        ).distinct()

        return busy_places

    @staticmethod
    def get_currently_busy_places(threshold: int = 60):
        """
        Finds places that are currently busy based on live busyness data.
        Uses actual live data from BestTimeLiveStatus instead of forecasted data.

        Args:
            threshold (int): Busyness percentage (0-100) to be considered "busy".

        Returns:
            QuerySet[Place]: A queryset of Place objects.
        """
        from busy_times.models import BestTimeLiveStatus

        # Get places with live busyness data above threshold
        busy_live_statuses = BestTimeLiveStatus.objects.filter(
            venue_info__bt_forecast_available=True,
            live_percentage__isnull=False,
            live_percentage__gte=threshold,
            # Only consider recent live data (within last hour)
            last_fetched_at__gte=timezone.now() - datetime.timedelta(hours=1)
        )

        # Get the place IDs from these live statuses
        busy_place_ids = busy_live_statuses.values_list(
            'venue_info__place_id', flat=True)

        # Return the places
        currently_busy_places = Place.objects.filter(
            id__in=busy_place_ids
        ).distinct()

        return currently_busy_places

    @staticmethod
    def fetch_busyness(place: Place):
        if not place.should_fetch_busyness():
            return False

        try:
            busy_times_services = BusyTimesServices()

            # --- Ensure BestTimeVenueInfo exists ---
            current_bt_info = place.besttime_info if hasattr(
                place, 'besttime_info') else None
            if not current_bt_info:
                print(
                    f"BestTimeVenueInfo not found for {place.name}, attempting to fetch/create.")
                # Call your existing service method
                current_bt_info = busy_times_services.fetch_and_store_besttime_venue_info(
                    place)
                if not current_bt_info:
                    return False
                # Update the place instance in memory with the new/fetched BestTimeVenueInfo
                place.besttime_info = current_bt_info

            # Now place.besttime_info is guaranteed to be populated if the above didn't error out.

            should_fetch_live = True
            live_status_record = None

            # --- Conditional Live Data Fetch ---
            if place.besttime_info and hasattr(place.besttime_info, 'live_status') and place.besttime_info.live_status:
                live_status_record = place.besttime_info.live_status

            if live_status_record and live_status_record.last_fetched_at:
                place_local_now = place.get_current_time_in_place_timezone()
                if not place_local_now:
                    place_local_now = timezone.localtime(timezone.now())

                last_fetched_utc = live_status_record.last_fetched_at
                last_fetched_in_place_tz = last_fetched_utc.astimezone(
                    place.timezone)

                if (last_fetched_in_place_tz.year == place_local_now.year and
                        last_fetched_in_place_tz.month == place_local_now.month and
                        last_fetched_in_place_tz.day == place_local_now.day and
                        last_fetched_in_place_tz.hour == place_local_now.hour):
                    should_fetch_live = False
                    print(
                        f"Skipping live data fetch for {place.name}, already fetched this local hour ({place_local_now.hour}) at {last_fetched_in_place_tz.strftime('%Y-%m-%d %H:%M:%S %Z')}.")

            if should_fetch_live:
                print(f"Fetching live data for {place.name}.")
                busy_times_services.fetch_and_store_besttime_live_status(
                    place)

            # Only fetch weekly forecast if the place's forecast is unavailable or hasn't been fetched recently
            if not place.besttime_info.bt_forecast_available or \
               (place.besttime_info.bt_forecast_updated_at and
                    (timezone.now() - place.besttime_info.bt_forecast_updated_at).days >= 7 and
                    place.besttime_info.updated_at < (timezone.now() - datetime.timedelta(days=1))):
                print(f"Fetching weekly forecast for {place.name}.")
                busy_times_services.fetch_and_store_besttime_weekly_forecast(
                    place.besttime_info)
                busy_times_services.fetch_and_store_besttime_week_overview(
                    place.besttime_info)
                busy_times_services.fetch_and_store_besttime_week_raw_data(
                    place.besttime_info)
            elif not place.besttime_info.week_overview_data:
                print(f"Fetching week overview for {place.name}.")
                busy_times_services.fetch_and_store_besttime_week_overview(
                    place.besttime_info)
            elif not place.besttime_info.week_raw_data:
                print(f"Fetching week raw data for {place.name}.")
                busy_times_services.fetch_and_store_besttime_week_raw_data(
                    place.besttime_info)
            else:
                print(
                    f"Skipping weekly forecast fetch for {place.name}, already fetched recently.")

            refreshed_place = Place.objects.select_related(
                'besttime_info', 'besttime_info__live_status'
            ).prefetch_related(
                "primary_type", 'besttime_info__daily_forecasts'
            ).get(pk=place.pk)

            return refreshed_place

        except RateLimitException:
            return False
        except Exception as e:
            print(e)
            return False


@background(schedule=15)
def schedule_busyness_fetch(place_id: str):
    place = Place.objects.get(hash_id=place_id)
    if place:
        return PlacesServices().fetch_busyness(place)
    else:
        return None
