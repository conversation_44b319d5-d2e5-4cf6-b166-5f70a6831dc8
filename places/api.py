import datetime
import pytz
from rest_framework import viewsets, status, parsers, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from events.models import EventInstance, EventRSVP
from events.serializers import EventInstancePlaceSerializer, EventBaseSerializer
from guytrax.pagination import LongListPagination, MiniListPagination
from places.services import LatLngBounds, PlacesServices
from busy_times.services import BusyTimesServices
from rate_limits.services import RateLimitException
from django.contrib.gis.geos import Point
from django.db.models import Q, Prefetch
from profiles.models import Profile
from reviews.models import CheckIn, Review
from tags.models import Tag, TagType
from tags.serializers import TagList<PERSON>erializer
from views.models import PlaceView
from .serializers import *
from guytrax.serializers import PhotoUploadSerializer, ToggleSerializer
from .models import Place, PlacePhoto, PlaceType, PlaceVisit
from django.contrib.gis.db.models.functions import Distance
from reviews.serializers import CheckInSerializer, CheckInFormSerializer, CheckInRequestSerializer, CheckInSerializer, ReviewSerializer, ReviewRequestSerializer, ReviewSerializer
from django.utils import timezone
from views.models import PlaceView


class PlaceTypeViewSet(viewsets.ModelViewSet):
    permission_classes = (IsAuthenticated,)
    queryset = PlaceType.objects.all().order_by('name').filter(browsable=True)
    serializer_class = PlaceTypeSerializer

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def names(self, request):
        return Response(PlaceType.objects.filter(browsable=True).values_list("name", flat=True))


class PlaceViewSet(viewsets.ModelViewSet):
    permission_classes = (IsAuthenticated,)
    queryset = Place.objects.all()
    serializer_class = PlaceListSerializer
    pagination_class = MiniListPagination
    parser_classes = (parsers.MultiPartParser,
                      parsers.FormParser, parsers.JSONParser)
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'address', 'normalized_name']

    def get_queryset(self):
        qs = super().get_queryset()
        # Add select_related for foreign keys to reduce queries
        qs = qs.select_related("added_by").prefetch_related("primary_type",
                                                            "hours", "tags", "photos__uploaded_by"
                                                            )

        serializer_class = self.get_serializer_class()
        action_is_fetch_busyness = self.action == 'fetch_busyness'

        needs_any_busyness_prefetch = (
            serializer_class == PlaceWithBusynessSerializer or
            serializer_class == AdminPlaceDetailSerializer or
            serializer_class == PlaceBaseSerializer or
            action_is_fetch_busyness
        )
        if self.request.query_params.get('include_busyness_details') == 'true':
            needs_any_busyness_prefetch = True

        if needs_any_busyness_prefetch:
            qs = qs.select_related(
                'besttime_info',
                'besttime_info__live_status'
            ).prefetch_related(
                'besttime_info__daily_forecasts'
            )
        return qs.order_by('created')

    def get_serializer_class(self):
        if self.action == 'update' or self.action == 'partial_update':
            return PlaceUpdateRequestSerializer
        return super().get_serializer_class()

    def get_permissions(self):
        if self.action == 'list' or self.action == 'update' or self.action == 'partial_update':
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_object(self, queryset=None):
        hash_value = self.kwargs.get("pk")
        if hash_value:
            return self.get_queryset().get(hash_id=hash_value)
        else:
            return super().get_object(queryset)

    def get_user_profile(self):
        """Cache profile lookup to avoid repeated queries"""
        if not hasattr(self, '_cached_profile'):
            self._cached_profile = self.request.user.profiles.select_related(
                'user').first()
        return self._cached_profile

    def retrieve(self, request, *args, **kwargs):
        hash_value = kwargs.get("pk")
        try:
            # The location can either be in the request or in the request headers
            location_data = None
            if hasattr(request, 'location'):
                location_data = request.location
            elif request.META.get('Location') or request.META.get('X-Location'):
                location_data = request.META.get(
                    'Location') or request.META.get('X-Location')
            if location_data:
                point = Point(location_data['lng'],
                              location_data['lat'], srid=4326)
                place = self.get_queryset().annotate(
                    distance=Distance("coordinates", point)
                ).get(hash_id=hash_value)
            else:
                place = self.get_object()
        except Place.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        profile = self.get_user_profile()
        if not place.approved and not (request.user.is_superuser or place.added_by == profile):
            return Response(status=status.HTTP_404_NOT_FOUND)

        # Use get_or_create to avoid duplicate view records
        PlaceView.objects.get_or_create(place=place, profile=profile)

        # Fetch place busyness data if applicable
        if place.is_open() and place.should_fetch_busyness():
            PlacesServices().fetch_busyness(place)
        if request.user.is_superuser:
            serializer = AdminPlaceDetailSerializer(
                place, context={'request': request, 'profile': profile})
        else:
            serializer = PlaceDetailSerializer(
                place, context={'request': request, 'profile': profile})
        return Response(serializer.data)

    def patch(self, request, *args, **kwargs):
        return self.partial_update(request, *args, **kwargs)

    @action(methods=['put'], detail=True, serializer_class=ToggleSerializer)
    def favorite(self, request, pk=None):
        enabled = request.data.get("enabled")
        place = self.get_object()
        user = self.request.user
        try:
            profile = user.profiles.first()
            if enabled:
                profile.favorite_places.add(place)
            else:
                profile.favorite_places.remove(place)
            profile.save()
        except Exception as e:
            print(e)
            return Response(status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_200_OK)

    @action(methods=['post'], detail=True, serializer_class=PhotoUploadSerializer)
    def upload_cover(self, request, pk=None):
        serializer = PhotoUploadSerializer(data=request.data)
        if serializer.is_valid():
            file = serializer.validated_data.get('photo')
            place = self.get_object()
            user = self.request.user
            place.logo = file
            place.save()
            if place.logo:
                return Response(place.logo.url, status=status.HTTP_201_CREATED)
            else:
                return Response(status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(methods=['post'], detail=True, serializer_class=PhotoUploadSerializer)
    def upload_photo(self, request, pk=None):
        serializer = PhotoUploadSerializer(data=request.data)
        if serializer.is_valid():
            file = serializer.validated_data.get('photo')
            caption = serializer.validated_data.get('caption')
            place = self.get_object()
            user = self.request.user
            photo = PlacePhoto()
            photo.caption = caption
            photo.uploaded_by = user.profiles.first()
            photo.place = place
            photo.url.save(file.name, file)
            if photo:
                return Response(photo.url.url, status=status.HTTP_201_CREATED)
            else:
                return Response(status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], serializer_class=PlaceRadiusRequestSerializer)
    def nearest(self, request):
        serializer = PlaceRadiusRequestSerializer(data=request.data)
        if serializer.is_valid():
            profile = Profile.objects.select_related('user').prefetch_related(
                "favorite_places"
            ).only("id", "user_id", "favorite_places").filter(
                user=request.user
            ).first()

            lat = serializer.validated_data.get("lat")
            lng = serializer.validated_data.get("lng")
            point = Point(lng, lat, srid=4326)

            places = self.paginate_queryset(PlacesServices.nearest_places(
                types=serializer.validated_data.get("types", None),
                tags=serializer.validated_data.get("tags", None),
                point=point,
                distance=serializer.validated_data.get("distance", 5),
                unit=serializer.validated_data.get("unit"),
                count=serializer.validated_data.get("count", 15),
            ))
            output_serializer = PlaceDistancedListSerializer(
                places, many=True, context={"coordinates": point, 'request': request, 'profile': profile})
            return self.get_paginated_response(output_serializer.data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def autocomplete(self, request):
        substring = request.query_params.get('q', None)
        if substring:
            places = PlacesServices.fuzzy_search(substring)
            user = self.request.user
            places = self.paginator.paginate_queryset(places, request)
            return self.get_paginated_response(PlaceSearchSerializer(places, many=True, context={'request': request, 'profile': user.profiles.first()}).data)
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], serializer_class=PlaceFilteredRadiusRequestSerializer, pagination_class=LongListPagination)
    def search(self, request):
        serializer = PlaceFilteredRadiusRequestSerializer(data=request.data)
        if serializer.is_valid():
            user = self.request.user
            bounds = serializer.data.get("bounds", None)
            if bounds:
                bounds = LatLngBounds(
                    ne_lat=bounds['ne_lat'],
                    ne_lng=bounds['ne_lng'],
                    sw_lat=bounds['sw_lat'],
                    sw_lng=bounds['sw_lng'],
                )
            profile = Profile.objects.filter(user=user).prefetch_related(
                "favorite_places").only("favorite_places").first()
            lat = serializer.data.get("lat")
            lng = serializer.data.get("lng")
            point = Point(lng, lat, srid=4326)
            places = self.paginate_queryset(PlacesServices.nearest_places(
                name=serializer.data.get("name", None),
                types=serializer.data.get("types", None),
                tags=serializer.data.get("tags", None),
                point=point,
                bounds=bounds,
                only_browsable=(bounds is not None),
                distance=serializer.data.get("distance"),
                unit=serializer.data.get("unit"),
                count=serializer.data.get("count", 15),
                serves_food=serializer.data.get("serves_food", None),
                contactless_payments=serializer.data.get(
                    "contactless_payments", None),
                accepts_cc=serializer.data.get("accepts_cc", None),
                open_now=serializer.data.get("open_now", None),
                favorited=serializer.data.get("favorited", None),
                profile=profile,
                busy_now=serializer.data.get("busy_now", None),
            ))

            output_serializer = PlaceDistancedListSerializer(
                places, many=True, context={"coordinates": point, 'request': request, 'profile': profile})
            return self.get_paginated_response(output_serializer.data)
        else:
            print("Invalid serializer data:", serializer.errors)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], serializer_class=PlaceListSerializer, permission_classes=[IsAdminUser], url_path="approvals")
    def needs_approval(self, request):
        places = self.paginate_queryset(Place.objects.filter(
            approved=False, rejection_reason__isnull=True).order_by("-created"))
        serializer = PlaceListSerializer(places, many=True)
        return self.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'], serializer_class=EventBaseSerializer)
    def events(self, request, *args, **kwargs):
        profile = self.get_user_profile()
        hash_value = kwargs.get("pk")

        query = EventInstance.objects.upcoming().public().select_related(
            'place', 'parent'
        ).prefetch_related(
            "tags",
            Prefetch(
                "rsvps",
                queryset=EventRSVP.objects.select_related(
                    'profile').filter(profile=profile),
                to_attr="user_rsvps"
            )
        ).filter(place__hash_id=hash_value)
        events = self.paginate_queryset(query)
        serializer = EventInstancePlaceSerializer(events, many=True)
        return self.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'], serializer_class=EventBaseSerializer, url_path="events/past")
    def past_events(self, request, *args, **kwargs):
        profile = self.request.user.profiles.first()
        hash_value = kwargs.get("pk")
        query = EventInstance.past().filter(
            place__hash_id=hash_value).prefetch_related("tags", "parent", Prefetch(
                "rsvps",
                queryset=EventRSVP.objects.filter(profile=profile),
                to_attr="user_rsvps"
            ))
        events = self.paginate_queryset(query)
        serializer = EventInstancePlaceSerializer(events, many=True)
        return self.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'], serializer_class=ReviewSerializer(many=True))
    def reviews(self, request, *args, **kwargs):
        reviews = self.paginate_queryset(Review.objects.filter(place__hash_id=kwargs.get("pk")).order_by(
            "-created").prefetch_related("categories", "categories__category", "availabilities", "availabilities__category", "profile"))
        serializer = ReviewSerializer(reviews, many=True)
        return self.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'], serializer_class=CheckInSerializer(many=True))
    def check_ins(self, request, *args, **kwargs):
        check_ins = CheckIn.objects.filter(place__hash_id=kwargs.get("pk")).order_by(
            "-created").prefetch_related("categories", "categories__category", "availabilities", "availabilities__category", "profile")
        serializer = CheckInSerializer(check_ins, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path="check-in/form", serializer_class=CheckInRequestSerializer)
    def check_in(self, request, *args, **kwargs):
        place = self.get_object()
        possibilites = place.check_in_possibilities()
        if possibilites:
            serializer = CheckInFormSerializer(possibilites)
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'], url_path="check-in", url_name="Check in", serializer_class=CheckInRequestSerializer)
    def check_in_post(self, request, *args, **kwargs):
        place_hash_id = self.kwargs.get("pk")
        request.data["place"] = place_hash_id
        serializer = CheckInRequestSerializer(data=request.data)
        if serializer.is_valid():
            review = serializer.save()
            if review:
                return Response("Check-In created", status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], url_path="review", serializer_class=ReviewRequestSerializer)
    def review(self, request, *args, **kwargs):
        place_hash_id = self.kwargs.get("pk")
        request.data["place"] = place_hash_id
        serializer = ReviewRequestSerializer(data=request.data)
        if serializer.is_valid():
            # review = serializer.create(serializer.validated_data)
            review = serializer.save()
            if review:
                return Response("Review created", status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], url_path="similar")
    def similar(self, request, *args, **kwargs):
        place = self.get_object()
        similar_places = self.paginate_queryset(
            PlacesServices.find_similar_places(place))
        serializer = SimilarPlaceSerializer(
            similar_places, many=True, context={'request': request, 'place': place})
        return self.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[IsAdminUser])
    def tags(self, request, *args, **kwargs):
        place = self.get_object()
        tags = place.tags.all()
        serializer = TagSerializer(tags, many=True)
        return Response(serializer.data)

    @tags.mapping.post
    def add_tags(self, request, *args, **kwargs):
        place = self.get_object()
        serializer = TagListSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        if len(serializer.validated_data.get("tags")) == 0:
            return Response("No tags provided", status=status.HTTP_400_BAD_REQUEST)
        tag_names = serializer.validated_data.get("tags")
        found_tags = []
        new_tags = []
        for tag_name in tag_names:
            tag, created = Tag.objects.get_or_create(
                name=tag_name, defaults={"public": True, "type": TagType.PLACE})
            if created:
                new_tags.append(tag)
            found_tags.append(tag)
        place.tags.set(found_tags)
        place.save()
        return Response(
            {"new_tags": TagSerializer(new_tags, many=True).data,
             "tags": TagSerializer(found_tags, many=True).data},
            status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'], url_path="visit", serializer_class=RecordPlaceVisitSerializer)
    def record_visit(self, request, *args, **kwargs):
        place = self.get_object()
        profile = request.user.profiles.first()
        has_visited = profile.visits.filter(place=place).exists()
        if has_visited:
            return Response("Visit already recorded", status=status.HTTP_208_ALREADY_REPORTED)
        serializer = RecordPlaceVisitSerializer(data=request.data)
        if serializer.is_valid():
            arrival_time = serializer.validated_data.get("arrival_time", None)
            departure_time = serializer.validated_data.get(
                "departure_time", None)
            device_id = request.headers.get("DeviceId", None)
            location = profile.coordinates
            PlaceVisit.record_visit(place=place, point=location, profile=profile, arrival_time=arrival_time,
                                    departure_time=departure_time, passive_record=False, device_id=device_id)
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], url_path="fetch_busyness")
    def fetch_busyness(self, request, *args, **kwargs):
        place = self.get_object()
        if not place.should_fetch_busyness():
            return Response({"detail": "Busyness data not applicable for this place type."},
                            status=status.HTTP_400_BAD_REQUEST)
        try:
            places_services = PlacesServices()
            places_services.fetch_busyness(place)
            refreshed_place = Place.objects.select_related(
                'besttime_info', 'besttime_info__live_status'
            ).prefetch_related(
                "primary_type", 'besttime_info__daily_forecasts'
            ).get(pk=place.pk)
            serializer = PlaceWithBusynessSerializer(
                refreshed_place, context={'request': request})
            return Response(serializer.data, status=status.HTTP_200_OK)

        except RateLimitException:
            return Response({"detail": "Rate limit exceeded. Please try again later."},
                            status=status.HTTP_429_TOO_MANY_REQUESTS)
        except Exception as e:
            print(e)
            return Response({"detail": "An unexpected error occurred while fetching busyness data."},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='bounds', serializer_class=PlaceRadiusRequestSerializer)
    def get_by_bounds(self, request):
        serializer = PlaceRadiusRequestSerializer(data=request.data)
        if serializer.is_valid():
            user = self.request.user
            profile = Profile.objects.filter(user=user).prefetch_related(
                "favorite_places").only("favorite_places").first()
            ne_lat = serializer.data.get("ne_lat")
            ne_lng = serializer.data.get("ne_lng")
            sw_lat = serializer.data.get("sw_lat")
            sw_lng = serializer.data.get("sw_lng")
            places = self.paginate_queryset(PlacesServices.nearest_places(
                bounds=LatLngBounds(
                    ne_lat=ne_lat,
                    ne_lng=ne_lng,
                    sw_lat=sw_lat,
                    sw_lng=sw_lng,
                ),
                types=serializer.data.get("types", None),
                tags=serializer.data.get("tags", None),
            ))
            output_serializer = PlaceListSerializer(
                places, many=True, context={'request': request, 'profile': profile})
            return self.get_paginated_response(output_serializer.data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
