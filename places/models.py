from functools import reduce
import re
import os
from uuid import uuid4
from PIL import Image
import calendar
from math import floor
from django.contrib.gis.db import models
from django.dispatch import receiver
from django.db.models.signals import pre_save
from django_hashids import HashidsField
import urllib.request

import pytz
from timezonefinder import TimezoneFinder
from busy_times.models import BestTimeDailyForecast, BestTimeVenueInfo
from guytrax.constants import DaysOfTheWeek
from photos.models import UserUploadedPhoto
from places.constants import PlaceSource
from django.contrib.postgres.fields import ArrayField
from timezone_field import TimeZoneField
from django.contrib import admin
from reviews.models import RatingCategoryBoolean, RatingCategoryRange
from tags.models import Tag, SuggestedTag
from django.db.models import Count
from dateutil import relativedelta
from django.utils import timezone
from django.contrib.gis.db.models.functions import Distance
from datetime import datetime, time, timedelta
from django.contrib.gis import geos, measure
from django.conf import settings


def get_timezone_from_location(latitude, longitude):
    tf = TimezoneFinder()
    return tf.timezone_at(lng=longitude, lat=latitude)


class PlaceType(models.Model):

    def path_and_rename(instance, filename):
        filename = f"{uuid4().hex}.png"
        return os.path.join("uploads/place_types", filename)

    name = models.CharField(unique=True)
    image = models.ImageField(null=True, blank=True, upload_to=path_and_rename)
    material_icon = models.CharField(max_length=32, null=True, blank=True)
    possible_rating_category_ranges = models.ManyToManyField(
        RatingCategoryRange, blank=True)
    possible_rating_category_toggles = models.ManyToManyField(
        RatingCategoryBoolean, blank=True)
    browsable = models.BooleanField(default=True)
    fetch_busyness_forecast = models.BooleanField(default=False)

    def __str__(self):
        return self.name

    def to_json(self):
        return {
            "id": self.id,
            "name": self.name,
            "image": self.image.url if self.image else None,
            "material_icon": self.material_icon,
        }

    class Meta:
        ordering = ['name']


class PlaceManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(closed=False).prefetch_related("hours", "primary_type")


class Place(models.Model):
    hash_id = HashidsField(real_field_name="id")
    manager = models.ForeignKey(
        "profiles.Profile", null=True, on_delete=models.SET_NULL, blank=True)

    # Basic Info
    name = models.CharField()
    normalized_name = models.CharField(blank=True, null=True)
    street_address = models.CharField(blank=True, null=True)
    address = models.CharField()
    logo = models.ImageField(null=True, blank=True,
                             upload_to='uploads/places/')
    tagline = models.TextField(null=True, blank=True, max_length=256)
    summary = models.TextField(null=True, blank=True)
    tags = models.ManyToManyField(Tag, blank=True)
    menu_link = models.CharField(null=True, blank=True)
    like_summary = models.TextField(null=True, blank=True)
    dislike_summary = models.TextField(null=True, blank=True)

    # Type
    primary_type = models.ForeignKey(
        PlaceType,
        on_delete=models.SET_NULL,  # Or models.PROTECT
        null=True,  # MUST be nullable for this step
        blank=True,
        related_name='primary_places'
    )
    secondary_types = models.ManyToManyField(
        PlaceType,
        blank=True,
        related_name='secondary_places'
    )

    # Legacy support for existing code
    @property
    def type(self) -> list[PlaceType]:
        if self.primary_type is None:
            return list(self.secondary_types.all())
        return [self.primary_type] + list(self.secondary_types.all())

    # Source info
    source = models.PositiveSmallIntegerField(
        choices=PlaceSource.choices, default=0)
    source_id = models.CharField(null=True, blank=True)
    events_url = models.CharField(null=True, blank=True)
    events_fetch_prompt = models.TextField(null=True, blank=True)
    last_content_fetched = models.DateTimeField(null=True, blank=True)

    # Contact Info
    website = models.CharField(null=True, blank=True)
    phone = models.CharField(null=True, blank=True)
    email = models.EmailField(null=True, blank=True)

    # Location and Hours
    coordinates = models.PointField(geography=True, null=True)
    radius = models.FloatField(null=True, blank=True, default=50)
    timezone = TimeZoneField(default="US/Eastern")
    opening_day = models.DateField(null=True, blank=True)
    closing_day = models.DateField(null=True, blank=True)

    age_minimum = models.PositiveSmallIntegerField(null=True, blank=True)

    # Flags
    browsable = models.BooleanField(default=False, db_index=True)
    explorable = models.BooleanField(default=True)
    closed = models.BooleanField(default=False)
    featured = models.BooleanField(default=False)
    serves_food = models.BooleanField(default=False)
    contactless_payments = models.BooleanField(default=False)
    cash_only = models.BooleanField(default=False)
    transit_point = models.BooleanField(default=False)
    fake = models.BooleanField(default=False)
    should_fetch_busyness_forecast = models.BooleanField(default=False)
    can_fetch_busyness_forecast = models.BooleanField(default=True)

    # Ownership / Allyship
    lgbt_owned = models.BooleanField(null=True, blank=True)
    lgbt_staff = models.BooleanField(null=True, blank=True)
    woman_owned = models.BooleanField(null=True, blank=True)
    poc_owned = models.BooleanField(null=True, blank=True)

    # Attribution
    added_by = models.ForeignKey(
        "profiles.Profile", null=True, on_delete=models.SET_NULL, blank=True, related_name="places_added")
    approved = models.BooleanField(default=False)
    rejection_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(null=True, blank=True)

    # Timestamps
    updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    objects = PlaceManager()
    raw_objects = models.Manager()

    def update_timezone(self):
        self.timezone = get_timezone_from_location(
            self.coordinates.y, self.coordinates.x)
        self.save()

    def is_new(self) -> bool:
        if self.opening_day is None:
            return False
        today = timezone.now().astimezone(self.timezone).date()
        three_months_ago = today - relativedelta.relativedelta(months=3)
        return self.opening_day >= three_months_ago

    def is_24_hours(self) -> bool:
        hours = self.todays_hours()
        if hours is None:
            return False
        else:
            return hours.open_day == hours.open_time == hours.close_day == hours.close_time

    def todays_hours(self):
        if not self.hours.exists():
            return None
        now = timezone.now().astimezone(self.timezone)
        all_hours = self.hours.all()
        if all_hours.count() == 1:
            only = all_hours.first()
            if only.open_day == only.close_day and only.open_time == only.close_time:
                return only
        for hours in all_hours:
            open = hours.translated_open(self.timezone)
            close = hours.translated_close(self.timezone)
            if open <= now and close >= now or open >= now:
                return hours
        return None

    def has_ai_content(self) -> bool:
        # If there is no manager for this place and there is a last_content_fetched, we assume it's AI generated
        return self.manager is None and self.last_content_fetched is not None

    @admin.display(boolean=True, description="Open Now")
    def is_open(self) -> bool:
        if self.is_24_hours():
            return True
        now = timezone.now().astimezone(self.timezone)
        hours = self.todays_hours()
        if hours:
            open_time = hours.translated_open(self.timezone)
            close_time = hours.translated_close(self.timezone)
            return open_time <= now and now < close_time
        else:
            return False

    def opening_time(self) -> datetime:
        if self.is_24_hours():
            return None
        if self.is_open():
            return None
        elif self.todays_hours():
            return self.todays_hours().translated_open(self.timezone)
        else:
            return None

    def closing_time(self) -> datetime:
        if self.is_24_hours():
            return None
        elif not self.is_open():
            return None
        elif self.todays_hours():
            return self.todays_hours().translated_close(self.timezone)
        else:
            return None

    def hours_on_day(self, date: datetime):
        day = (date.weekday() + 1) % 7
        for hours in self.hours.all():
            if hours.open_day == day:
                return hours
        print(f"Found no hours for {day}")
        return None

    def location(self) -> dict:
        return {"lat": self.coordinates.y,
                "lng": self.coordinates.x}

    def get_similar_by_tags(self, distance: float = 15):
        # Get the tags of the current post
        place_tags = self.tags.all()

        # Get the coordinates of the current place
        place_coordinates = self.coordinates

        # Define a distance radius (e.g., 5 miles)
        from django.contrib.gis.measure import D
        distance_radius = D(mi=distance)

        # Find other posts with similar tags
        similar_places = Place.objects.filter(
            tags__in=place_tags,
            coordinates__distance_lte=(
                place_coordinates, distance_radius, "spheroid"),
            approved=True,
            browsable=True,
        ).exclude(id=self.id).prefetch_related("tags", "primary_type", "hours").annotate(
            distance=Distance("coordinates", place_coordinates),
        )

        # Annotate with the number of matching tags
        similar_places = similar_places.annotate(
            matching_tag_count=Count('tags')).filter(matching_tag_count__gt=2).order_by('-matching_tag_count', 'distance')

        return similar_places

    def check_in_possibilities(self):
        if not self.type.exists():
            return None
        types = self.type.all().prefetch_related("possible_rating_category_ranges",
                                                 "possible_rating_category_toggles").all()
        if len(types) > 1:
            toggles = reduce(lambda x, y: x.possible_rating_category_toggles.all() | y.possible_rating_category_toggles.all(),
                             types)
            ranges = reduce(lambda x, y: x.possible_rating_category_ranges.all() | y.possible_rating_category_ranges.all(),
                            types)
        else:
            toggles = types[0].possible_rating_category_toggles.all()
            ranges = types[0].possible_rating_category_ranges.all()
        return {
            "id": self.hash_id,
            "name": self.name,
            "ranges": list(dict.fromkeys(ranges)),
            "toggles": list(dict.fromkeys(toggles)),
            "types": types,
            "tags": self.tags.all().values_list('name', flat=True)
        }

    def image_gen_prompt(self):
        tags = list(self.tags.values_list('name', flat=True))
        comma_separator = ", "
        slash_separator = "/"
        return f"An abstract, nondescript, monochrome thumbnail photo of people at an lgbt {self.primary_type.name} with the following tags: {comma_separator.join(tags)}"

    def should_fetch_busyness(self):
        """Checks if this Place should fetch busyness data."""
        return self.should_fetch_busyness_forecast and self.can_fetch_busyness_forecast and self.approved

    def should_display_busyness(self):
        """Checks if any of this Place's PlaceTypes allow busyness forecasts (using prefetched data)."""
        if not hasattr(self, '_prefetched_objects_cache') or 'type' not in self._prefetched_objects_cache:
            return False  # 'type' wasn't prefetched

        place_types = self._prefetched_objects_cache["primary_type"]
        return any(pt.fetch_busyness_forecast for pt in place_types) and self.can_fetch_busyness_forecast

    def get_live_busyness_data(self):
        """Retrieves formatted live busyness data if available."""
        # Access besttime_info using the related_name from BestTimeVenueInfo
        if hasattr(self, 'besttime_info') and self.besttime_info and \
           hasattr(self.besttime_info, 'live_status') and self.besttime_info.live_status:
            live = self.besttime_info.live_status

            # Check if essential data (percentage) is present
            if live.live_percentage is not None:
                return {
                    "percentage": live.live_percentage,
                    # This might be None if only /live is used
                    "status_text": live.live_status_text,
                    "normal_range_min": live.live_normal_percentage_min,  # Might be None
                    "normal_range_max": live.live_normal_percentage_max,  # Might be None
                    "delta_from_forecast": live.live_delta_percentage,
                    "last_fetched_at": live.last_fetched_at,
                    "source_updated_at": live.source_updated_at
                }
        return None

    def get_current_time_in_place_timezone(self) -> datetime | None:
        """Returns the current time localized to the place's timezone."""
        try:
            place_tz_str = self.timezone
            if not place_tz_str:
                place_tz_str = settings.TIME_ZONE

            place_timezone_obj = pytz.timezone(place_tz_str)
            utc_now = timezone.now()
            return utc_now.astimezone(place_timezone_obj)
        except pytz.UnknownTimeZoneError:
            default_tz_str = getattr(settings, 'TIME_ZONE', 'UTC')
            try:
                default_tz = pytz.timezone(default_tz_str)
                return timezone.now().astimezone(default_tz)
            except pytz.UnknownTimeZoneError:
                return timezone.now()
        except Exception:
            return timezone.localtime(timezone.now())

    # Return type hint
    def get_current_day_forecast(self) -> BestTimeDailyForecast | None:
        """Gets the forecast object for the current day in the place's local timezone."""
        if hasattr(self, 'besttime_info') and self.besttime_info and self.besttime_info.bt_forecast_available:
            place_local_now = self.get_current_time_in_place_timezone()
            if not place_local_now:
                return None

            current_day_int = place_local_now.weekday()
            try:
                # Access daily_forecasts via the related_name on besttime_info
                return self.besttime_info.daily_forecasts.get(day_int=current_day_int)
            except BestTimeDailyForecast.DoesNotExist:
                return None
            except AttributeError:  # If daily_forecasts relation doesn't exist
                return None
        return None

    def get_daily_busyness_forecasts(self):
        """
        Helper method to get all daily busyness forecasts for this place.
        Returns a queryset of BestTimeDailyForecast objects or None.
        """
        if hasattr(self, 'besttime_info') and self.besttime_info and self.besttime_info.bt_forecast_available:
            # Assumes daily_forecasts are ordered by 'day_int' in BestTimeDailyForecast.Meta
            return self.besttime_info.daily_forecasts.all()
        return None

    def get_busyness_forecast_for_day(self, day_int_value: int):
        """
        Helper method to get the busyness forecast for a specific day.
        day_int_value: 0 for Monday, ..., 6 for Sunday.
        Returns a single BestTimeDailyForecast object or None.
        """
        daily_busyness_forecasts = self.get_daily_busyness_forecasts()
        if daily_busyness_forecasts:
            try:
                # Ensure BestTimeDailyForecast is the correct model name used in your project
                return daily_busyness_forecasts.get(day_int=day_int_value)
            except BestTimeDailyForecast.DoesNotExist:
                return None
        return None

    def import_place_data(self, data: dict):
        # Handle summary
        if self.summary is None or self.summary == "":
            summary = data.get("description")
            summary = re.sub(r'\[.*?\]', '', summary)
            self.summary = summary

        # Handle tags
        ai_tags = data.get("tags")
        ai_suggested_tags = data.get("suggested_tags")

        tags = Tag.objects.filter(name__in=ai_tags)
        self.tags.add(*tags)

        # If there are any tags that don't already exist they should be added to ai_suggested_tags
        ai_suggested_tags.extend(
            list(set(ai_tags) - set(tags.values_list('name', flat=True))))

        # Handle suggested tags
        if ai_suggested_tags:
            from django.contrib.contenttypes.models import ContentType
            content_type = ContentType.objects.get_for_model(self)
            for tag in ai_suggested_tags:
                # Check if the tag already exists
                tag = Tag.objects.filter(name=tag).first()
                if tag:
                    self.tags.add(tag)
                    continue
                try:
                    SuggestedTag.objects.get_or_create(
                        name=tag,
                        content_type=content_type,
                        object_id=self.id
                    )
                except Exception as e:
                    print(f"Failed to create suggested tag: {e}")

        # Handle likes and dislikes
        self.like_summary = re.sub(
            r'\[.*?\]', '', data.get("likes", self.like_summary))
        self.dislike_summary = re.sub(
            r'\[.*?\]', '', data.get("dislikes", self.dislike_summary))

        # Update the last fetched time and save the place
        self.last_content_fetched = timezone.now()
        self.save()

    @property
    def has_available_busyness_forecast(self):
        """Checks if BestTime.app busyness forecast data is available for this place."""
        return hasattr(self, 'besttime_info') and self.besttime_info and self.besttime_info.bt_forecast_available

    @property
    def busyness_forecast_last_updated_on(self):
        """Returns the timestamp of when the BestTime.app busyness forecast was last updated."""
        if hasattr(self, 'besttime_info') and self.besttime_info:
            return self.besttime_info.bt_forecast_updated_at
        return None

    def __str__(self):
        return self.name

    class Meta:
        indexes = [
            models.Index(fields=['approved', 'created']),
        ]


@receiver(pre_save, sender=Place)
def update_normalized_name(sender, instance, **kwargs):
    instance.normalized_name = instance.name.lower().replace(".", "").replace("!", "").replace("+", " and ").replace(
        "'", "").replace(":", "").replace(",", "").replace("-", " ").replace("&", " and ").replace("  ", " ").strip()


@receiver(pre_save, sender=Place)
def cleanup_place_types(sender, instance, **kwargs):
    if instance.primary_type is None and instance.secondary_types.exists():
        instance.primary_type = instance.secondary_types.first()
        instance.secondary_types.remove(instance.primary_type)
    if instance.primary_type in instance.secondary_types.all():
        instance.secondary_types.remove(instance.primary_type)


class PlaceHours(models.Model):
    open_day = models.SmallIntegerField(choices=DaysOfTheWeek.choices)
    open_time = models.SmallIntegerField()
    close_day = models.SmallIntegerField(choices=DaysOfTheWeek.choices)
    close_time = models.SmallIntegerField(null=True)
    place = models.ForeignKey(
        Place, related_name="hours", on_delete=models.CASCADE)

    def __relative_date(self, weekday, timevalue: time, given_timezone, reference: datetime = None):
        weekday = (weekday + 6) % 7
        timevalue = timevalue.replace(tzinfo=None)
        if reference is None:
            reference = timezone.now()
        reference = reference.astimezone(given_timezone)
        hour, minute = timevalue.hour, timevalue.minute
        days = (reference.weekday() - weekday) % 7
        date = (reference - timedelta(days=days)).replace(
            hour=int(hour), minute=int(minute), second=0, microsecond=0)
        return date.astimezone(given_timezone)

    def translated_open(self, tz=None) -> datetime:
        if tz is None:
            tz = timezone.get_fixed_timezone(0)
        time_units = time(hour=floor(self.open_time/100),
                          minute=abs(self.open_time) % 100)
        return self.__relative_date(self.open_day, time_units, tz)

    def translated_close(self, tz=None, d: datetime = None) -> datetime:
        if tz is None:
            tz = timezone.get_fixed_timezone(0)
        time_units = time(hour=floor(self.close_time/100),
                          minute=abs(self.close_time) % 100)
        if self.open_day == self.close_day and self.open_time == self.close_time:
            # We can assume this place is open 24 hours
            return self.__relative_date(self.close_day + 7, time_units, tz, d)
        else:
            # Otherwise the close day should always be >= the open day
            open = self.__relative_date(self.open_day, time_units, tz, d)
            close = self.__relative_date(self.close_day + 7, time_units, tz, d)
            if close < open:
                return close + timedelta(days=7)
            else:
                return close

    def __str__(self) -> str:
        open_weekday = calendar.day_name[self.open_day]
        opening_time = self.translated_open()
        close_weekday = calendar.day_name[self.close_day]
        closing_time = self.translated_close()
        return f"{open_weekday} at {opening_time.strftime('%-I:%M%p')} - {close_weekday} at {closing_time.strftime('%-I:%M%p')}"

    class Meta:
        verbose_name_plural = "place hours"
        ordering = ["open_day", "open_time"]


class PlacePhoto(UserUploadedPhoto):

    def path_and_rename(instance, filename):
        filename = f"{uuid4().hex}-{instance.place.id}"
        return os.path.join("uploads/places", filename)

    url = models.ImageField(null=False, blank=False,
                            upload_to=path_and_rename)
    uploaded_by = models.ForeignKey(
        "profiles.Profile", null=False, on_delete=models.CASCADE)
    place = models.ForeignKey(
        Place, null=False, on_delete=models.CASCADE, related_name="photos")

    ratio = models.FloatField(blank=True, null=True)

    def __str__(self) -> str:
        return self.url.url

    def save(self, *args, **kwargs):
        if self.url:
            try:
                with Image.open(self.url) as img:
                    self.photo_ratio = float(img.width) / float(img.height)
            except Exception as e:
                print(e)
        super().save(*args, **kwargs)


class PlaceVisit(models.Model):
    hash_id = HashidsField(real_field_name="id")
    place = models.ForeignKey(
        Place, on_delete=models.CASCADE, related_name="visits")
    profile = models.ForeignKey(
        "profiles.Profile", on_delete=models.CASCADE, related_name="visits")
    arrival_time = models.DateTimeField(null=True, blank=True)
    departure_time = models.DateTimeField(null=True, blank=True)
    passive_record = models.BooleanField(default=True)
    valid = models.BooleanField(null=True)
    validated = models.DateTimeField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    recorded_distance = models.FloatField(null=True, blank=True)
    device_id = models.CharField(max_length=255, null=True, blank=True)
    point = models.PointField(geography=True, null=True, blank=True, srid=4326)
    notification_sent_at = models.DateTimeField(
        null=True, blank=True, db_index=True)

    @classmethod
    def record_visit(self, point: geos.Point, place: Place, profile, arrival_time=None, departure_time=None, passive_record=True, device_id: str = None):
        from geopy import distance as d
        if passive_record:
            recent_visit = PlaceVisit.objects.filter(
                profile=profile, place=place).order_by("-arrival_time").first()
            if arrival_time is None and passive_record == False:
                arrival_time = datetime.now(tz=timezone.get_fixed_timezone(0))
            radius = place.radius if place.radius is not None else 100
            distance = d.geodesic(point, place.coordinates).meters
            if distance > radius:
                return
            if recent_visit and recent_visit.place == place and recent_visit.departure_time is None and (recent_visit.arrival_time is None or (recent_visit.arrival_time is not None and recent_visit.arrival_time > (arrival_time - timedelta(hours=6)))):
                if recent_visit.recorded_distance > distance:
                    recent_visit.recorded_distance = distance
                    recent_visit.save()
                return
            valid = None
            validated = None
        else:
            distance = d.geodesic(point, place.coordinates).meters
            valid = True
            validated = datetime.now(tz=timezone.get_fixed_timezone(0))
        PlaceVisit.objects.create(place=place, profile=profile,
                                  arrival_time=arrival_time, passive_record=passive_record, recorded_distance=distance, device_id=device_id, valid=valid, validated=validated)

    @classmethod
    def record_departure(self, place: Place, profile, time=None, passive_record=True):
        recent_visit = PlaceVisit.objects.filter(
            profile=profile, place=place).order_by("-arrival_time").first()
        time = time if time is not None else datetime.now(
            tz=timezone.get_fixed_timezone(0))
        if recent_visit and recent_visit.place == place and recent_visit.arrival_time > (time - timedelta(hours=6)):
            recent_visit.departure_time = time
            recent_visit.save()
            return

    class Meta:
        ordering = ['-arrival_time']
