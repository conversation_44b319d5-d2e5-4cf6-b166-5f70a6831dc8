from zoneinfo import available_timezones
import datetime
from django.utils import timezone
from django.conf import settings
from rest_framework import serializers
from busy_times.models import BestTimeDailyForecast
from guytrax.serializers import UnitRadiusRequestSerializer
from photos.serializers import PhotoSerializer
from profiles.serializers import ProfileMiniSerializer
from tags.models import Tag
from tags.serializers import MatchingTagSerializer, TagSerializer
from .models import Place, PlaceHours, PlacePhoto, PlaceType, PlaceVisit


class PlaceTypeSerializer(serializers.ModelSerializer):

    class Meta:
        model = PlaceType
        fields = ["id", "name", "image", "material_icon", "browsable"]
        read_only_fields = ["id"]

    def create(self, validated_data):
        instance, created = PlaceType.objects.get_or_create(**validated_data)
        return instance


class PlaceHoursSerializer(serializers.ModelSerializer):
    open = serializers.ReadOnlyField(source="translated_open")
    close = serializers.ReadOnlyField(source="translated_close")

    class Meta:
        model = PlaceHours
        fields = ["open", "close"]


class RelatedPlaceSerializer(serializers.ModelSerializer):
    id = serializers.CharField(source='hash_id', read_only=True)
    logo = serializers.SerializerMethodField()
    stock_photo = serializers.SerializerMethodField()
    type = PlaceTypeSerializer(many=True, read_only=True)

    def get_logo(self, obj) -> dict | None:
        if obj.logo and hasattr(obj.logo, 'url'):
            return {"id": 0, "url": obj.logo.url}
        return None

    def get_stock_photo(self, obj) -> bool:
        return obj.logo is not None

    class Meta:
        model = Place
        fields = ["id", "name", "address", "logo",
                  "stock_photo", "location", "type"]
        read_only_fields = ["id"]


class PlaceBaseSerializer(serializers.ModelSerializer):
    id = serializers.CharField(source='hash_id', read_only=True)
    stock_photo = serializers.SerializerMethodField(read_only=True)
    primary_type = PlaceTypeSerializer()
    type = PlaceTypeSerializer(many=True, read_only=True)
    logo = serializers.SerializerMethodField()
    suggestion_reason = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()
    is_live_busy_now = serializers.SerializerMethodField()
    is_usually_busy_now = serializers.SerializerMethodField()
    usually_busy_times_today = serializers.SerializerMethodField()
    live_busyness_delta = serializers.SerializerMethodField()

    # Constants for busyness logic
    LIVE_DATA_RECENCY_MINUTES = 60
    BUSY_THRESHOLD_PERCENTAGE = 65  # e.g., 65% or higher is considered "busy"
    USUAL_BUSY_INTENSITY_THRESHOLD = 1

    def get_is_live_busy_now(self, obj: Place) -> bool | None:
        """Checks live data percentage against threshold if recent."""
        if not obj.should_display_busyness():
            return None

        live_data = obj.get_live_busyness_data()
        if live_data and "percentage" in live_data and live_data.get("percentage") is not None and live_data.get("last_fetched_at"):
            if live_data['last_fetched_at'] >= timezone.now() - datetime.timedelta(minutes=self.LIVE_DATA_RECENCY_MINUTES):
                return live_data['percentage'] >= self.BUSY_THRESHOLD_PERCENTAGE
        return None

    def get_live_busyness_delta(self, obj: Place) -> int | None:
        """Returns the live busyness delta from normal when busy."""
        if not obj.should_display_busyness():
            return None
        live_data = obj.get_live_busyness_data()
        if live_data and "delta_from_forecast" in live_data and live_data.get("delta_from_forecast") is not None and live_data.get("last_fetched_at"):
            if live_data['last_fetched_at'] >= timezone.now() - datetime.timedelta(minutes=self.LIVE_DATA_RECENCY_MINUTES):
                return live_data['delta_from_forecast']
        return None

    def get_is_usually_busy_now(self, obj: Place) -> bool | None:
        """Checks forecast intensity number for the current hour against threshold (using prefetched data)."""
        if not obj.should_display_busyness():
            return None

        if hasattr(obj, 'besttime_info') and obj.besttime_info and obj.besttime_info.bt_forecast_available:
            place_local_now = obj.get_current_time_in_place_timezone()
            if not place_local_now:
                return None

            current_day_int = place_local_now.weekday()
            current_hour_index = place_local_now.hour  # Hour (0-23)

            if hasattr(obj.besttime_info, '_prefetched_objects_cache') and 'daily_forecasts' in obj.besttime_info._prefetched_objects_cache:
                daily_forecasts = obj.besttime_info._prefetched_objects_cache['daily_forecasts']
                daily_forecast = next(
                    (df for df in daily_forecasts if df.day_int == current_day_int),
                    None
                )

                if daily_forecast:
                    hour_analysis = None
                    if daily_forecast.hour_analysis_data and isinstance(daily_forecast.hour_analysis_data, list):
                        for hour_data in daily_forecast.hour_analysis_data:
                            if isinstance(hour_data, dict) and hour_data.get('hour') == current_hour_index:
                                hour_analysis = hour_data
                                break

                    if hour_analysis:
                        intensity_nr = hour_analysis.get('intensity_nr')
                        if isinstance(intensity_nr, int):
                            return intensity_nr >= self.USUAL_BUSY_INTENSITY_THRESHOLD

            return None

        return None

    def get_usually_busy_times_today(self, obj: Place) -> list[list[int]] | None:
        """Returns a list of busy hour ranges for today, handling wrap-around across midnight."""
        if not obj.should_display_busyness():
            return None

        if hasattr(obj, 'besttime_info') and obj.besttime_info and obj.besttime_info.bt_forecast_available:
            place_local_now = obj.get_current_time_in_place_timezone()
            if not place_local_now:
                return None

            current_day_int = place_local_now.weekday()

            if hasattr(obj.besttime_info, '_prefetched_objects_cache') and 'daily_forecasts' in obj.besttime_info._prefetched_objects_cache:
                daily_forecasts = obj.besttime_info._prefetched_objects_cache['daily_forecasts']
                daily_forecast = next(
                    (df for df in daily_forecasts if df.day_int == current_day_int),
                    None
                )

                if daily_forecast:
                    busy_hours = []
                    if daily_forecast.hour_analysis_data and isinstance(daily_forecast.hour_analysis_data, list):
                        for hour_data in sorted(daily_forecast.hour_analysis_data, key=lambda x: x.get('hour', -1)):
                            hour = hour_data.get('hour')
                            intensity_nr = hour_data.get('intensity_nr')

                            if intensity_nr is not None and intensity_nr != 999 and isinstance(hour, int) and intensity_nr >= self.USUAL_BUSY_INTENSITY_THRESHOLD:
                                busy_hours.append(hour)

                    if not busy_hours:
                        return []

                    busy_ranges = []
                    current_range = [busy_hours[0], busy_hours[0] + 1]

                    for i in range(1, len(busy_hours)):
                        if busy_hours[i] == current_range[1]:
                            current_range[1] += 1
                        else:
                            busy_ranges.append(current_range)
                            current_range = [busy_hours[i], busy_hours[i] + 1]
                    busy_ranges.append(current_range)

                    # Handle wrap-around across midnight
                    if len(busy_ranges) > 1 and busy_ranges[0][0] == 0 and busy_ranges[-1][1] == 24:
                        merged_range = [busy_ranges[-1][0], busy_ranges[0][1]]
                        # Remove the original separate ranges
                        busy_ranges = [r for i, r in enumerate(
                            busy_ranges) if i != 0 and i != len(busy_ranges) - 1]
                        busy_ranges.append(merged_range)
                        # Ensure the merged range is correctly formatted as [start, end] inclusive
                        final_busy_ranges = [[r[0], r[1] - 1]
                                             for r in busy_ranges]
                        return final_busy_ranges
                    else:
                        final_busy_ranges = [[r[0], r[1] - 1]
                                             for r in busy_ranges]
                        return final_busy_ranges

            return None

        return None

    def get_distance(self, obj) -> float | None:
        if hasattr(obj, "distance"):
            return obj.distance.mi
        return None

    def get_suggestion_reason(self, obj) -> str | None:
        if hasattr(obj, "suggestion_reason"):
            return obj.suggestion_reason
        return None

    def get_stock_photo(self, obj) -> bool:
        return obj.logo is not None

    def get_logo(self, obj) -> dict | None:
        if obj.logo and hasattr(obj.logo, 'url'):
            return {"id": 0, "url": obj.logo.url}

        if hasattr(obj, 'primary_type'):
            place_type_with_image = obj.primary_type
            if place_type_with_image and hasattr(place_type_with_image.image, 'url'):
                return {"id": 0, "url": place_type_with_image.image.url}
        return None

    class Meta:
        model = Place
        fields = ["id",
                  "name",
                  "address",
                  "primary_type",
                  "type",
                  "logo",
                  "stock_photo",
                  "suggestion_reason",
                  "distance",
                  "is_live_busy_now",
                  "is_usually_busy_now",
                  "usually_busy_times_today",
                  "live_busyness_delta",
                  "has_ai_content",
                  ]
        read_only_fields = ["id"]


class PlaceSearchSerializer(PlaceBaseSerializer):

    class Meta(PlaceBaseSerializer.Meta):
        fields = PlaceBaseSerializer.Meta.fields + ["location"]
        read_only_fields = PlaceBaseSerializer.Meta.read_only_fields + \
            ["location"]


class PlaceMiniSerializer(PlaceBaseSerializer):
    distance = serializers.SerializerMethodField()

    def get_distance(self, obj) -> float:
        if hasattr(obj, "distance"):
            return obj.distance.mi
        else:
            return None

    class Meta:
        model = Place
        fields = PlaceBaseSerializer.Meta.fields + [
            "distance"]
        read_only_fields = ["distance"]


class PlacePhotoSerializer(PhotoSerializer):
    place = PlaceMiniSerializer()

    class Meta:
        model = PlacePhoto
        fields = PhotoSerializer.Meta.fields + ["place"]


class PlaceSerializer(PlaceMiniSerializer):
    photos = PhotoSerializer(many=True)
    is_favorite = serializers.SerializerMethodField()
    tags = TagSerializer(many=True)
    matching_tags = serializers.SerializerMethodField()
    can_check_in = serializers.SerializerMethodField()

    def get_can_check_in(self, obj):
        if obj.is_open() and hasattr(obj, "distance"):
            if obj.radius is not None:
                can_check_in = obj.distance.m < max(
                    settings.DEFAULT_CHECK_IN_RADIUS, obj.radius)
            else:
                can_check_in = obj.distance.m < settings.DEFAULT_CHECK_IN_RADIUS
            return can_check_in
        return False

    def get_matching_tags(self, obj) -> list[dict]:
        profile = self.context.get('profile')
        if profile and (profile.tags or profile.interests):
            if hasattr(profile, '_prefetched_objects_cache') and 'tags' in profile._prefetched_objects_cache and 'interests' in profile._prefetched_objects_cache:
                # Use prefetched tag data for this user
                tags = profile._prefetched_objects_cache['tags'].values_list(
                    'id', flat=True)
                interests = profile._prefetched_objects_cache['interests'].values_list(
                    'id', flat=True)
            else:
                tags = profile.tags.all().values_list('id', flat=True)
                interests = profile.interests.all().values_list('id', flat=True)

            user_tags = set(tags)
            user_interests = set(interests)

            # Combine user tags and interests
            user_tag_ids = user_tags.union(user_interests)

            # Filter obj.tags based on the combined set
            matching_tags = [
                tag for tag in obj.tags.all() if tag.id in user_tag_ids]

            return MatchingTagSerializer(matching_tags, many=True).data
        return []

    def get_is_favorite(self, obj) -> bool:
        favorite = self.context.get("favorite", None)
        if favorite is not None:
            return favorite
        profile = self.context.get('profile')
        if profile:
            return profile.favorite_places.contains(obj)
        else:
            return None

    class Meta:
        model = Place
        fields = PlaceMiniSerializer.Meta.fields + [
            "tagline",
            "summary",
            "website",
            "phone",
            "email",
            "is_open",
            "is_24_hours",
            "location",
            "photos",
            "tags",
            "matching_tags",
            "is_favorite",
            "is_new",
            "serves_food",
            "menu_link",
            "can_check_in",
            "contactless_payments",
            "cash_only",
            "like_summary",
            "dislike_summary",
        ]


class PlaceUpdateRequestSerializer(PlaceSerializer):
    type = serializers.PrimaryKeyRelatedField(
        queryset=PlaceType.objects.all(), many=True)
    tags = serializers.PrimaryKeyRelatedField(
        queryset=Tag.objects.all(), many=True)

    class Meta:
        model = Place
        fields = [
            "name",
            "address",
            "tagline",
            "summary",
            "website",
            "phone",
            "email",
            "location",
            "type",
            "tags",
            "logo",
            "opening_day",
            "closing_day",
            "age_minimum",
            "approved",
            "browsable",
            "explorable",
            "closed",
            "featured",
            "serves_food",
            "contactless_payments",
            "cash_only",
            "transit_point",
            "lgbt_owned",
            "lgbt_staff",
            "woman_owned",
            "poc_owned",
            "menu_link",
            "events_url",
            "events_fetch_prompt",
            "should_fetch_busyness_forecast",
        ]


class PlaceListSerializer(PlaceSerializer):
    class Meta:
        model = Place
        fields = list(set(PlaceSerializer.Meta.fields + [
            "opening_time",
            "closing_time",
            "browsable",
            "featured",
        ]).difference(["photos"]))


class SimilarPlaceSerializer(PlaceSerializer):
    matching_tags = serializers.SerializerMethodField()
    similarity_score = serializers.SerializerMethodField()

    def get_similarity_score(self, obj) -> float:
        if hasattr(obj, "final_score"):
            return obj.final_score
        return 0.0

    def get_matching_tags(self, obj) -> list[dict]:
        place = self.context.get('place')
        if place:
            matching_tags = [
                tag for tag in obj.tags.all() if tag in place.tags.all()]
            return MatchingTagSerializer(matching_tags, many=True).data
        return []

    class Meta:
        model = Place
        fields = list(set(PlaceSerializer.Meta.fields).difference(["photos"])) + \
            ["similarity_score"]


class PlaceDetailSerializer(PlaceSerializer):
    events = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    timezone = serializers.ChoiceField(choices=available_timezones())
    hours = PlaceHoursSerializer(
        many=True,
        read_only=True,
    )
    visited = serializers.SerializerMethodField()
    last_visit = serializers.SerializerMethodField()
    todays_events = serializers.SerializerMethodField()
    live_busyness_status = serializers.SerializerMethodField()
    current_forecasted_status = serializers.SerializerMethodField()
    today_forecast_summary = serializers.SerializerMethodField()
    week_forecast_overview = serializers.SerializerMethodField()
    weekly_hourly_forecasts = serializers.SerializerMethodField()

    # Day integer to text mapping
    DAY_INT_TO_TEXT = {
        0: "Monday", 1: "Tuesday", 2: "Wednesday", 3: "Thursday",
        4: "Friday", 5: "Saturday", 6: "Sunday"
    }

    def _should_include_busyness(self, obj: Place) -> bool:
        if not obj.should_display_busyness():
            print(f"PlaceType not configured for busyness: {obj.name}.")
            return False
        if not hasattr(obj, 'besttime_info') or not obj.besttime_info:
            print(f"BestTimeVenueInfo not found for {obj.name}.")
            return False
        return True

    def get_live_busyness_status(self, obj: Place) -> dict | None:
        """Returns detailed live busyness data if available and recent."""
        if not self._should_include_busyness(obj):
            return None

        live_data = obj.get_live_busyness_data()

        if live_data and live_data.get('last_fetched_at'):
            if live_data['last_fetched_at'] < timezone.now() - datetime.timedelta(minutes=self.LIVE_DATA_RECENCY_MINUTES):
                # Modify the returned dict to indicate data is old
                old_live_data = live_data.copy()
                old_live_data["status_text"] = "Live data available but considered too old for detailed display (older than {} min)".format(
                    self.LIVE_DATA_RECENCY_MINUTES)
                # Remove fields that might be misleading if data is old? Or keep them? Keeping them for now.
                return old_live_data
        return live_data  # Returns full live_data if recent, or None if no live_data

    def get_current_forecasted_status(self, obj: Place) -> dict | None:
        """Returns the detailed forecasted status for the current hour."""
        if not self._should_include_busyness(obj) or \
           not (hasattr(obj.besttime_info, 'bt_forecast_available') and obj.besttime_info.bt_forecast_available):
            return None

        place_local_now = obj.get_current_time_in_place_timezone()
        if not place_local_now:
            return None

        current_day_int = place_local_now.weekday()
        current_hour_index = place_local_now.hour

        try:
            daily_forecast = obj.besttime_info.daily_forecasts.get(
                day_int=current_day_int)

            hour_analysis = None
            if daily_forecast.hour_analysis_data and isinstance(daily_forecast.hour_analysis_data, list):
                for hour_data in daily_forecast.hour_analysis_data:
                    if isinstance(hour_data, dict) and hour_data.get('hour') == current_hour_index:
                        hour_analysis = hour_data
                        break

            if hour_analysis:
                # Return the relevant hour analysis data
                return {
                    "day": daily_forecast.day_text,
                    "hour": current_hour_index,
                    "intensity_nr": hour_analysis.get('intensity_nr'),
                    "intensity_txt": hour_analysis.get('intensity_txt'),
                    # Add other fields from hour_analysis if needed
                }
        except BestTimeDailyForecast.DoesNotExist:
            return None
        except (IndexError, TypeError, AttributeError):
            return None
        return None

    def get_today_forecast_summary(self, obj: Place) -> dict | None:
        """Returns a summary of today's forecast based on the new structure."""
        if not self._should_include_busyness(obj) or \
           not (hasattr(obj.besttime_info, 'bt_forecast_available') and obj.besttime_info.bt_forecast_available):
            return None

        daily_forecast = obj.get_current_day_forecast()  # Gets today's forecast object

        if daily_forecast:
            peak_hours_texts = []
            # Use the peak_hours_data field which stores the list of peak objects
            if daily_forecast.peak_hours_data and isinstance(daily_forecast.peak_hours_data, list):
                for peak in daily_forecast.peak_hours_data:
                    if isinstance(peak, dict):
                        # Attempt to get text representations if available, fallback to hour numbers
                        start_hour = peak.get('peak_start')
                        end_hour = peak.get('peak_end')
                        # Simple formatting (could be improved with AM/PM)
                        start_text = f"{start_hour}:00" if start_hour is not None else "N/A"
                        end_text = f"{end_hour}:00" if end_hour is not None else "N/A"
                        peak_hours_texts.append(
                            f"{start_text} - {end_text} (intensity: {peak.get('peak_intensity', 'N/A')})")

            # Use day_rank_mean/max if needed, or construct summary text differently
            # Example
            summary_text = f"Avg Rank: {daily_forecast.day_rank_mean}, Max Rank: {daily_forecast.day_rank_max}"

            return {
                "day": daily_forecast.day_text,
                # Replace with better summary if possible
                "overall_busyness_text": summary_text,
                "peak_hours_summary": peak_hours_texts,  # Renamed for clarity
                "busy_hours": daily_forecast.busy_hours_list,  # Directly pass the list
                "quiet_hours": daily_forecast.quiet_hours_list  # Directly pass the list
            }
        return None

    def get_weekly_hourly_forecasts(self, obj) -> list[dict] | None:
        """
        Returns the raw hourly busyness percentages for each day of the week.
        Assumes data is stored in obj.besttime_info.week_raw_data from the
        /forecasts/week/raw2 endpoint.
        """
        if not self._should_include_busyness(obj) or \
           not (hasattr(obj, 'besttime_info') and obj.besttime_info):
            return None

        # Assumes 'week_raw_data' stores the 'analysis.week_raw' array
        raw_week_data_list = getattr(obj.besttime_info, 'week_raw_data', None)

        if not isinstance(raw_week_data_list, list) or not raw_week_data_list or len(raw_week_data_list) != 7:
            # print(f"Warning: week_raw_data for {obj.name} is missing or not a list.") # Use logging
            return None

        processed_weekly_forecasts = []
        # Ensure we have a placeholder for all 7 days, even if data is missing for some
        day_data_map = {i: None for i in range(7)}

        for day_entry in raw_week_data_list:
            if isinstance(day_entry, dict):
                day_int = day_entry.get("day_int")
                day_raw_percentages = day_entry.get("day_raw")

                if isinstance(day_int, int) and 0 <= day_int <= 6 and \
                   isinstance(day_raw_percentages, list) and \
                   all(isinstance(p, (int, float)) or p is None for p in day_raw_percentages):

                    # Ensure day_raw_percentages has 24 items, padding with None if necessary
                    hourly_data = (day_raw_percentages + [None] * 24)[:24]

                    day_data_map[day_int] = {
                        "day_int": day_int,
                        "day_text": self.DAY_INT_TO_TEXT.get(day_int, "Unknown Day"),
                        "hourly_percentages": hourly_data
                    }
                else:
                    # print(f"Warning: Invalid item structure in week_raw_data for {obj.name}: {day_entry}") # Use logging
                    pass

        # Convert map to list, ensuring all days are represented
        for i in range(7):
            if day_data_map[i]:
                processed_weekly_forecasts.append(day_data_map[i])
            else:  # Add placeholder if data for this day_int was missing
                processed_weekly_forecasts.append({
                    "day_int": i,
                    "day_text": self.DAY_INT_TO_TEXT.get(i, "Unknown Day"),
                    # Default to all None for missing day
                    "hourly_percentages": [None] * 24
                })

        return processed_weekly_forecasts

    def get_week_forecast_overview(self, obj: Place) -> list[dict] | None:
        """
        Returns a weekly forecast overview with daily average and max busyness percentages.
        Assumes the raw week overview data is stored in obj.besttime_info.week_overview_data
        """
        if not self._should_include_busyness(obj) or \
                not (hasattr(obj, 'besttime_info') and obj.besttime_info):
            return None

        # Assume the raw list from 'analysis.week_overview_data' is stored here
        # For example, in a JSONField on your BestTimeVenueInfo model.
        raw_week_data = getattr(obj.besttime_info, 'week_overview_data', None)

        if not isinstance(raw_week_data, list):
            # Log or handle the case where data is missing or not in the expected format
            # Use logging
            print(
                f"Warning: week_overview_data for {obj} is missing or not a list.")
            return None

        processed_week_overview = []
        for day_data in raw_week_data:
            if isinstance(day_data, dict):
                processed_week_overview.append({
                    "day_int": day_data.get("day_int"),
                    "day_text": day_data.get("day_text"),
                    # Already a percentage
                    "average_busyness_percentage": day_data.get("day_mean"),
                    # Already a percentage
                    "max_busyness_percentage": day_data.get("day_max"),
                    "day_rank_max": day_data.get("day_rank_max"),
                    "day_rank_mean": day_data.get("day_rank_mean"),
                    # Hour venue opens
                    "venue_open_hour": day_data.get("venue_open"),
                    # Hour venue closes
                    "venue_closed_hour": day_data.get("venue_closed")
                })
            else:
                # Log or handle malformed day_data item
                # Use logging
                print(
                    f"Warning: Invalid item in week_overview for {obj}: {day_data}")

        # Ensure it's sorted by day_int if not already guaranteed by the source
        processed_week_overview.sort(key=lambda x: x.get('day_int', 99))

        return processed_week_overview

    def get_todays_events(self, obj) -> list[dict]:
        from events.serializers import EventInstanceBaseSerializer
        today = timezone.now().astimezone(obj.timezone).today()
        events = obj.event_instances.filter(
            start_time__date=today)
        return EventInstanceBaseSerializer(events, many=True).data

    def get_last_visit(self, obj) -> datetime.datetime | None:
        profile = self.context.get('profile')
        if profile:
            visit = profile.visits.filter(place=obj).first()
            if visit:
                return visit.arrival_time
        return None

    def get_visited(self, obj) -> bool:
        profile = self.context.get('profile')
        if profile:
            return profile.visits.filter(place=obj, valid=True).exists()
        else:
            return None

    # similar_places = serializers.SerializerMethodField()

    # def get_similar_places(self, obj):
    #     return PlaceMiniSerializer(obj.get_similar_by_tags(), many=True, context=self.context).data

    class Meta:
        model = Place
        fields = PlaceSerializer.Meta.fields + [
            "hours",
            "timezone",
            "closing_time",
            "opening_time",
            "events",
            "distance",
            "can_check_in",
            "browsable",
            "explorable",
            "approved",
            "featured",
            "image_gen_prompt",
            "visited",
            "last_visit",
            "cash_only",
            "contactless_payments",
            "todays_events",
            "lgbt_owned",
            "lgbt_staff",
            "woman_owned",
            "poc_owned",
            "age_minimum",
            "live_busyness_status",
            "current_forecasted_status",
            "today_forecast_summary",
            "week_forecast_overview",
            "weekly_hourly_forecasts",
            "opening_day",
            "closing_day",
            "cash_only",
            "contactless_payments",
            "like_summary",
            "dislike_summary",
        ]


class AdminPlaceDetailSerializer(PlaceDetailSerializer):
    added_by = ProfileMiniSerializer()

    class Meta:
        model = Place
        fields = PlaceDetailSerializer.Meta.fields + [
            "added_by",
            "rejection_date",
            "rejection_reason",
            "created",
            "updated",
            "fake",
            "closed",
            "transit_point",
            "radius",
            "events_url",
            "events_fetch_prompt",
            "source",
            "source_id",
            "should_fetch_busyness_forecast",
        ]
        read_only_fields = ["id", "location"]


class PlaceDistancedListSerializer(PlaceListSerializer):
    distance = serializers.SerializerMethodField()

    class Meta(PlaceListSerializer.Meta):
        fields = PlaceListSerializer.Meta.fields + \
            ['distance', 'radius', 'location']

    def get_distance(self, obj) -> float:
        if hasattr(obj, "distance"):
            return obj.distance.mi
        else:
            return None


class PlaceRadiusRequestSerializer(UnitRadiusRequestSerializer):
    count = serializers.IntegerField(required=False, default=20, initial=20)
    types = serializers.PrimaryKeyRelatedField(
        queryset=PlaceType.objects.all(), required=False,
        many=True, allow_null=True)
    tags = serializers.PrimaryKeyRelatedField(
        queryset=Tag.objects.all(), required=False,
        many=True, allow_null=True)


class PlaceFilteredRadiusRequestSerializer(PlaceRadiusRequestSerializer):
    name = serializers.CharField(
        required=False, allow_null=True, allow_blank=True)
    serves_food = serializers.BooleanField(required=False, allow_null=True)
    contactless_payments = serializers.BooleanField(
        required=False, allow_null=True)
    accepts_cc = serializers.BooleanField(required=False, allow_null=True)
    open_now = serializers.BooleanField(required=False, allow_null=True)
    bounds = serializers.DictField(required=False, allow_null=True)
    favorited = serializers.BooleanField(required=False, allow_null=True)
    busy_now = serializers.BooleanField(required=False, allow_null=True)


class UpdatePlaceVisitSerializer(serializers.ModelSerializer):
    arrival_time = serializers.DateTimeField(required=False)
    departure_time = serializers.DateTimeField(required=False, allow_null=True)

    class Meta:
        model = PlaceVisit
        fields = ["arrival_time", "departure_time"]


class RecordPlaceVisitSerializer(serializers.Serializer):
    arrival_time = serializers.DateTimeField(required=False, allow_null=True)
    departure_time = serializers.DateTimeField(required=False, allow_null=True)


class PlaceVisitSerializer(serializers.ModelSerializer):
    id = serializers.CharField(source='hash_id')
    place = PlaceMiniSerializer()
    arrival_time = serializers.DateTimeField(
        default_timezone=timezone.get_fixed_timezone(0))
    departure_time = serializers.DateTimeField(
        default_timezone=timezone.get_fixed_timezone(0))
    validated = serializers.DateTimeField(
        default_timezone=timezone.get_fixed_timezone(0))

    class Meta:
        model = PlaceVisit
        fields = [
            "id",
            "place",
            "arrival_time",
            "departure_time",
            "passive_record",
            "valid",
            "validated"
        ]
        read_only_fields = ["id"]


class PlaceWithBusynessSerializer(PlaceBaseSerializer):
    live_busyness_status = serializers.SerializerMethodField()
    current_forecasted_status = serializers.SerializerMethodField()
    today_forecast_summary = serializers.SerializerMethodField()
    week_forecast_overview = serializers.SerializerMethodField()
    weekly_hourly_forecasts = serializers.SerializerMethodField()

    # Day integer to text mapping
    DAY_INT_TO_TEXT = {
        0: "Monday", 1: "Tuesday", 2: "Wednesday", 3: "Thursday",
        4: "Friday", 5: "Saturday", 6: "Sunday"
    }

    def _should_include_busyness(self, obj: Place) -> bool:
        if not obj.should_display_busyness():
            return False
        if not hasattr(obj, 'besttime_info') or not obj.besttime_info:
            return False
        return True

    def get_live_busyness_status(self, obj: Place) -> dict | None:
        """Returns detailed live busyness data if available and recent."""
        if not self._should_include_busyness(obj):
            return None

        live_data = obj.get_live_busyness_data()

        if live_data and live_data.get('last_fetched_at'):
            if live_data['last_fetched_at'] < timezone.now() - datetime.timedelta(minutes=self.LIVE_DATA_RECENCY_MINUTES):
                # Modify the returned dict to indicate data is old
                old_live_data = live_data.copy()
                old_live_data["status_text"] = "Live data available but considered too old for detailed display (older than {} min)".format(
                    self.LIVE_DATA_RECENCY_MINUTES)
                # Remove fields that might be misleading if data is old? Or keep them? Keeping them for now.
                return old_live_data
        return live_data  # Returns full live_data if recent, or None if no live_data

    def get_current_forecasted_status(self, obj: Place) -> dict | None:
        """Returns the detailed forecasted status for the current hour."""
        if not self._should_include_busyness(obj) or \
           not (hasattr(obj.besttime_info, 'bt_forecast_available') and obj.besttime_info.bt_forecast_available):
            return None

        place_local_now = obj.get_current_time_in_place_timezone()
        if not place_local_now:
            return None

        current_day_int = place_local_now.weekday()
        current_hour_index = place_local_now.hour

        try:
            daily_forecast = obj.besttime_info.daily_forecasts.get(
                day_int=current_day_int)

            hour_analysis = None
            if daily_forecast.hour_analysis_data and isinstance(daily_forecast.hour_analysis_data, list):
                for hour_data in daily_forecast.hour_analysis_data:
                    if isinstance(hour_data, dict) and hour_data.get('hour') == current_hour_index:
                        hour_analysis = hour_data
                        break

            if hour_analysis:
                # Return the relevant hour analysis data
                return {
                    "day": daily_forecast.day_text,
                    "hour": current_hour_index,
                    "intensity_nr": hour_analysis.get('intensity_nr'),
                    "intensity_txt": hour_analysis.get('intensity_txt'),
                    # Add other fields from hour_analysis if needed
                }
        except BestTimeDailyForecast.DoesNotExist:
            return None
        except (IndexError, TypeError, AttributeError):
            return None
        return None

    def get_today_forecast_summary(self, obj: Place) -> dict | None:
        """Returns a summary of today's forecast based on the new structure."""
        if not self._should_include_busyness(obj) or \
           not (hasattr(obj.besttime_info, 'bt_forecast_available') and obj.besttime_info.bt_forecast_available):
            return None

        daily_forecast = obj.get_current_day_forecast()  # Gets today's forecast object

        if daily_forecast:
            peak_hours_texts = []
            # Use the peak_hours_data field which stores the list of peak objects
            if daily_forecast.peak_hours_data and isinstance(daily_forecast.peak_hours_data, list):
                for peak in daily_forecast.peak_hours_data:
                    if isinstance(peak, dict):
                        # Attempt to get text representations if available, fallback to hour numbers
                        start_hour = peak.get('peak_start')
                        end_hour = peak.get('peak_end')
                        # Simple formatting (could be improved with AM/PM)
                        start_text = f"{start_hour}:00" if start_hour is not None else "N/A"
                        end_text = f"{end_hour}:00" if end_hour is not None else "N/A"
                        peak_hours_texts.append(
                            f"{start_text} - {end_text} (intensity: {peak.get('peak_intensity', 'N/A')})")

            # Use day_rank_mean/max if needed, or construct summary text differently
            # Example
            summary_text = f"Avg Rank: {daily_forecast.day_rank_mean}, Max Rank: {daily_forecast.day_rank_max}"

            return {
                "day": daily_forecast.day_text,
                # Replace with better summary if possible
                "overall_busyness_text": summary_text,
                "peak_hours_summary": peak_hours_texts,  # Renamed for clarity
                "busy_hours": daily_forecast.busy_hours_list,  # Directly pass the list
                "quiet_hours": daily_forecast.quiet_hours_list  # Directly pass the list
            }
        return None

    def get_weekly_hourly_forecasts(self, obj) -> list[dict] | None:
        """
        Returns the raw hourly busyness percentages for each day of the week.
        Assumes data is stored in obj.besttime_info.week_raw_data from the
        /forecasts/week/raw2 endpoint.
        """
        if not self._should_include_busyness(obj) or \
           not (hasattr(obj, 'besttime_info') and obj.besttime_info):
            return None

        # Assumes 'week_raw_data' stores the 'analysis.week_raw' array
        raw_week_data_list = getattr(obj.besttime_info, 'week_raw_data', None)

        if not isinstance(raw_week_data_list, list) or not raw_week_data_list or len(raw_week_data_list) != 7:
            # print(f"Warning: week_raw_data for {obj.name} is missing or not a list.") # Use logging
            return None

        processed_weekly_forecasts = []
        # Ensure we have a placeholder for all 7 days, even if data is missing for some
        day_data_map = {i: None for i in range(7)}

        for day_entry in raw_week_data_list:
            if isinstance(day_entry, dict):
                day_int = day_entry.get("day_int")
                day_raw_percentages = day_entry.get("day_raw")

                if isinstance(day_int, int) and 0 <= day_int <= 6 and \
                   isinstance(day_raw_percentages, list) and \
                   all(isinstance(p, (int, float)) or p is None for p in day_raw_percentages):

                    # Ensure day_raw_percentages has 24 items, padding with None if necessary
                    hourly_data = (day_raw_percentages + [None] * 24)[:24]

                    day_data_map[day_int] = {
                        "day_int": day_int,
                        "day_text": self.DAY_INT_TO_TEXT.get(day_int, "Unknown Day"),
                        "hourly_percentages": hourly_data
                    }
                else:
                    # print(f"Warning: Invalid item structure in week_raw_data for {obj.name}: {day_entry}") # Use logging
                    pass

        # Convert map to list, ensuring all days are represented
        for i in range(7):
            if day_data_map[i]:
                processed_weekly_forecasts.append(day_data_map[i])
            else:  # Add placeholder if data for this day_int was missing
                processed_weekly_forecasts.append({
                    "day_int": i,
                    "day_text": self.DAY_INT_TO_TEXT.get(i, "Unknown Day"),
                    # Default to all None for missing day
                    "hourly_percentages": [None] * 24
                })

        return processed_weekly_forecasts

    def get_week_forecast_overview(self, obj: Place) -> list[dict] | None:
        """
        Returns a weekly forecast overview with daily average and max busyness percentages.
        Assumes the raw week overview data is stored in obj.besttime_info.week_overview_data
        """
        if not self._should_include_busyness(obj) or \
                not (hasattr(obj, 'besttime_info') and obj.besttime_info):
            return None

        # Assume the raw list from 'analysis.week_overview_data' is stored here
        # For example, in a JSONField on your BestTimeVenueInfo model.
        raw_week_data = getattr(obj.besttime_info, 'week_overview_data', None)

        if not isinstance(raw_week_data, list) or not raw_week_data or len(raw_week_data) != 7:
            # Log or handle the case where data is missing or not in the expected format
            # Use logging
            print(
                f"Warning: week_overview_data for {obj} is missing or not a list.")
            return None

        processed_week_overview = []
        for day_data in raw_week_data:
            if isinstance(day_data, dict):
                processed_week_overview.append({
                    "day_int": day_data.get("day_int"),
                    "day_text": day_data.get("day_text"),
                    # Already a percentage
                    "average_busyness_percentage": day_data.get("day_mean"),
                    # Already a percentage
                    "max_busyness_percentage": day_data.get("day_max"),
                    "day_rank_max": day_data.get("day_rank_max"),
                    "day_rank_mean": day_data.get("day_rank_mean"),
                    # Hour venue opens
                    "venue_open_hour": day_data.get("venue_open"),
                    # Hour venue closes
                    "venue_closed_hour": day_data.get("venue_closed")
                })
            else:
                # Log or handle malformed day_data item
                # Use logging
                print(
                    f"Warning: Invalid item in week_overview for {obj}: {day_data}")

        # Ensure it's sorted by day_int if not already guaranteed by the source
        processed_week_overview.sort(key=lambda x: x.get('day_int', 99))

        return processed_week_overview

    class Meta(PlaceBaseSerializer.Meta):
        fields = PlaceBaseSerializer.Meta.fields + [
            "live_busyness_status",
            "current_forecasted_status",
            "today_forecast_summary",
            "week_forecast_overview",
            "weekly_hourly_forecasts"
        ]
