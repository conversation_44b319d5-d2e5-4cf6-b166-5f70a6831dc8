<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}
        GTX
      {% endblock %}
    </title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />

    <!-- Custom CSS -->
    <style>
      :root {
        --charcoal-gray: #36454f;
        --slate-blue: #6a5acd;
        --white: #ffffff;
        --light-gray: #f8f9fa;
        --hover-blue: #5b4bc4;
        --navbar-height: 60px;
      }
      
      body {
        background-color: var(--light-gray);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .navbar-custom {
        background-color: var(--charcoal-gray);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        height: var(--navbar-height);
      }
      
      .navbar-custom .navbar-brand {
        color: var(--white);
        font-weight: 600;
        font-size: 1.5rem;
      }
      
      .navbar-custom .navbar-brand:hover {
        color: var(--slate-blue);
      }
      
      .navbar-custom .navbar-nav .nav-link {
        color: var(--white);
        font-weight: 500;
        margin: 0 0.5rem;
        transition: color 0.3s ease;
      }
      
      .navbar-custom .navbar-nav .nav-link:hover {
        color: var(--slate-blue);
      }
      
      .navbar-custom .navbar-nav .nav-link.active {
        color: var(--slate-blue);
        font-weight: 600;
      }
      
      .navbar-toggler {
        border-color: var(--slate-blue);
      }
      
      .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
      }
      
      .container {
        max-width: 100%;
      }
      
      .main-content {
        padding-top: 2rem;
        min-height: calc(100vh - 76px);
      }
      
      .btn-primary-custom {
        background-color: var(--slate-blue);
        border-color: var(--slate-blue);
        color: var(--white);
      }
      
      .btn-primary-custom:hover {
        background-color: var(--hover-blue);
        border-color: var(--hover-blue);
        color: var(--white);
      }
      
      .card {
        border: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .text-slate-blue {
        color: var(--slate-blue);
      }
      
      .bg-slate-blue {
        background-color: var(--slate-blue);
      }
      
      .text-charcoal {
        color: var(--charcoal-gray);
      }
      
      .bg-charcoal {
        background-color: var(--charcoal-gray);
      }
    </style>

    {% block extra_css %}

    {% endblock %}
  </head>
  <body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
      <div class="container">
        <a class="navbar-brand" href="/">GTX</a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation"><span class="navbar-toggler-icon"></span></button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link" href="/">Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/manage/places/">Places</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/manage/events/">Events</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/manage/analytics/">Analytics</a>
            </li>
          </ul>

          <ul class="navbar-nav">
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Account</a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="/manage/profile/">Profile</a>
                </li>
                <li>
                  <a class="dropdown-item" href="/manage/locations/map/">Staypoints</a>
                </li>
                <li>
                  <a class="dropdown-item" href="/manage/settings/">Settings</a>
                </li>
                <li>
                  <hr class="dropdown-divider" />
                </li>
                <li>
                  <a class="dropdown-item" href="/admin/logout/">Logout</a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
      <div class="container-fluid">
        {% block content %}
          <div class="row">
            <div class="col-12">
              <h1 class="text-charcoal">Welcome to GTX</h1>
              <p class="text-muted">Select a section from the navigation above to get started.</p>
            </div>
          </div>
        {% endblock %}
      </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}

    {% endblock %}
  </body>
</html>
