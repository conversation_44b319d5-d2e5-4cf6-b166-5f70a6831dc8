from areas.serializers import AreaBaseSerializer, AreaSerializer
from dj.serializers import DJB<PERSON><PERSON>erializer, DJSerializer
from events.services import EventsServices
from photos.models import UserUploadedPhoto
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from guytrax.pagination import LongListPagination
from events.serializers import ProducerSerializer
from profiles.services import ProfileServices
from django.contrib.gis.geos import Point

from reviews.serializers import CheckInRequestSerializer, CheckInSerializer
from tags.models import Tag, TagType
from events.models import Event, EventInstance
from views.models import ProfileView
from .models import GenderIdentity, Profile, ProfilePhoto, SexualOrientation
from .serializers import ProfileDistancedSerializer, ProfileMiniSerializer, ProfilePhotoSerializer, ProfileSerializer
from guytrax.serializers import UnitRadiusRequestSerializer
from django.contrib.gis.db.models.functions import Distance
import datetime
from rest_framework import viewsets
from tags.serializers import TagSerializer
from rest_framework.decorators import action
from events.serializers import <PERSON>InstanceBaseSerializer, EventBaseSerializer, EventSuggestionSerializer, MyEventsSerializer, ParentEventSerializer
from places.serializers import PlaceBaseSerializer, PlaceListSerializer, PlaceMiniSerializer, PlaceSearchSerializer, PlaceVisitSerializer, UpdatePlaceVisitSerializer
from profiles.models import Profile, SocialGoal
from profiles.serializers import ProfileSerializer
from users.serializers import UserDetailSerializer, UserSerializer
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from guytrax.serializers import PointSerializer
from django.contrib.gis.geos import Point
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from django.db import transaction


class MyProfileViewSet(viewsets.GenericViewSet):
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Profile.objects.filter(
            user=self.request.user
        ).select_related(
            "gender_identity", "sexual_orientation", "primary_photo"
        ).prefetch_related(
            "social_goals", "photos", "my_tags", "my_interests"
        )

    def get_object(self):
        return self.get_queryset().first()

    def list(self, request, *args, **kwargs):
        currentUserProfile = self.get_object()
        if currentUserProfile:
            self.request.user.profile = currentUserProfile
            currentUserProfile.last_online = datetime.datetime.now(
                datetime.timezone.utc)
            currentUserProfile.save()
            serializer = UserDetailSerializer(
                self.request.user, context={'request': request})
            return Response(serializer.data)
        else:
            serializer = UserSerializer(self.request.user)
            return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        return self.patch_update(request, *args, **kwargs)

    # Override the delete method to make sure that it can only be done by the logged in profile owner
    def destroy(self, request, *args, **kwargs):
        profile = self.get_object()
        if profile.user.id != request.user.id:
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        profile.user.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @transaction.atomic
    @action(methods=['patch'], url_path="", detail=True)
    def patch_update(self, request, *args, **kwargs):
        profile = self.get_object()

        # Batch process tags and interests
        if "tags" in request.data:
            self._update_tags(profile, request.data.pop("tags"))
        if "interests" in request.data:
            self._update_interests(profile, request.data.pop("interests"))

        if "photo" in request.data:
            photo_file = request.data.get("photo")
            photo = ProfilePhoto.objects.create(
                url=photo_file, profile=profile)
            profile.primary_photo = photo
            profile.save()
            request.data.pop("photo")
        dob = request.data.get("dob", None)
        if dob:
            from datetime import datetime
            # Convert string to date object
            if isinstance(dob, str):
                dob = datetime.strptime(dob, '%Y-%m-%d').date()
            profile.dob = dob
            request.data.pop("dob")
        gender_identity_data = request.data.get("gender_identity", None)
        if gender_identity_data and gender_identity_data.get("id"):
            gender_identity = GenderIdentity.objects.get(
                id=gender_identity_data.get("id"))
            profile.gender_identity = gender_identity
            request.data.pop("gender_identity")
        sexual_orientation_data = request.data.get("sexual_orientation", None)
        if sexual_orientation_data and sexual_orientation_data.get("id"):
            sexual_orientation = SexualOrientation.objects.get(
                id=sexual_orientation_data.get("id"))
            profile.sexual_orientation = sexual_orientation
            request.data.pop("sexual_orientation")
        social_goals_data = request.data.get("social_goals", None)
        if social_goals_data:
            social_goal_ids = [
                goal.get("id") for goal in social_goals_data if goal.get("id")]
            social_goals = SocialGoal.objects.filter(id__in=social_goal_ids)
            profile.social_goals.set(social_goals)
            request.data.pop("social_goals")
        serializer = ProfileSerializer(
            profile, data=request.data, partial=True)
        pronouns = request.data.get("pronouns", None)
        if pronouns and pronouns == "":
            serializer.validated_data.pop("pronouns")
            profile.pronouns = None
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_202_ACCEPTED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _update_tags(self, profile, tag_names):
        """Optimized tag update with bulk operations"""
        existing_tags = {
            tag.name: tag for tag in Tag.objects.filter(name__in=tag_names)}
        new_tags = []
        for name in tag_names:
            if name not in existing_tags:
                new_tags.append(Tag(name=name))

        if new_tags:
            Tag.objects.bulk_create(new_tags, ignore_conflicts=True)

        all_tags = Tag.objects.filter(name__in=tag_names)
        profile.my_tags.set(all_tags)

    def _update_interests(self, profile, interest_names):
        """Optimized interest update with bulk operations"""
        existing_interests = {
            tag.name: tag for tag in Tag.objects.filter(name__in=interest_names)}
        new_interests = []
        for name in interest_names:
            if name not in existing_interests:
                new_interests.append(Tag(name=name))

        if new_interests:
            Tag.objects.bulk_create(new_interests, ignore_conflicts=True)

        all_interests = Tag.objects.filter(name__in=interest_names)
        profile.my_interests.set(all_interests)

    @method_decorator(cache_page(60 * 5))  # Cache for 5 minutes
    @action(methods=['get'], detail=False)
    def form(self, request):
        cache_key = "profile_form_data_v1"
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)

        from .serializers import GenderIdentitySerializer, SexualOrientationSerializer
        from .serializers import SocialGoalSerializer
        gender_identities = GenderIdentitySerializer(
            Profile.gender_identity.field.related_model.objects.all(), many=True)
        sexual_orientations = SexualOrientationSerializer(
            Profile.sexual_orientation.field.related_model.objects.all(), many=True)
        social_goals = SocialGoalSerializer(
            Profile.social_goals.field.related_model.objects.all(), many=True)
        from tags.models import Tag

        tags = TagSerializer(Tag.objects.filter(
            type__in=[TagType.GENERIC, TagType.PROFILE]), many=True)
        data = {
            "tags": tags.data,
            "gender_identities": gender_identities.data,
            "sexual_orientations": sexual_orientations.data,
            "social_goals": social_goals.data
        }
        cache.set(cache_key, data, 60 * 30)  # Cache for 30 minutes
        return Response(data)

    @action(methods=['post'], detail=False, serializer_class=PointSerializer)
    def favorites(self, request):
        serializer = PointSerializer(data=request.data)
        if serializer.is_valid():
            lat = serializer.data.get("lat")
            lng = serializer.data.get("lng")
            point = Point(lng, lat, srid=4326)
            profile = self.get_object()
            places = profile.favorite_places.prefetch_related("primary_type").annotate(
                distance=Distance("coordinates", point)).all().order_by('distance')
            place_serializer = PlaceBaseSerializer(
                places, many=True, context={'profile': profile, 'favorite': True})
            events = profile.favorite_events.select_related('place').prefetch_related('tags', "place__primary_type").all().annotate(
                distance=Distance("place__coordinates", point)).all().order_by('distance')
            event_serializer = EventBaseSerializer(
                events, many=True, context={"coordinates": point, 'profile': profile, 'favorite': True})
            area_serializer = AreaBaseSerializer(
                profile.favorite_areas.defer("shape").select_related('area_type').all(), many=True, context={'profile': profile, 'favorite': True})
            djs = profile.favorite_djs.select_related(
                "photo", "homebase", "homebase__area_type").all().defer("homebase__shape")
            dj_serializer = DJBaseSerializer(
                djs, many=True, context={'profile': profile, 'favorite': True})
            producers = profile.favorite_producers.all()
            producer_serializer = ProducerSerializer(
                producers, many=True, context={'profile': profile, 'favorite': True})
            return Response(data={"places": place_serializer.data, "events": event_serializer.data, "areas": area_serializer.data, "djs": dj_serializer.data, "producers": producer_serializer.data})
        else:
            return Response(serializer.errors)

    @action(detail=False, methods=['put'], url_path="photo/order")
    def reorder_photos(self, request):
        profile = self.get_object()
        photo_ids = request.data.get("photoIds")
        for index, photo_id in enumerate(photo_ids):
            photo = profile.photos.get(id=photo_id)
            photo.order = index
            photo.save()
        return Response(status=status.HTTP_202_ACCEPTED)

    @action(detail=False, methods=['get'], url_path=r'photos')
    def photos(self, request):
        profile = self.get_object()
        photos = profile.photos.all()
        return Response(ProfilePhotoSerializer(photos, many=True).data)

    @photos.mapping.post
    def upload_photo(self, request):
        profile = self.get_object()
        photo_file = request.data.get("photo")
        photo = ProfilePhoto.objects.create(
            url=photo_file, profile=profile)
        if photo:
            return Response(ProfilePhotoSerializer(photo, many=False).data)
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path=r'photos/(?P<photoId>\w+)')
    def get_photo(self, request, photoId=None):
        profile = self.get_object()
        photo = profile.photos.get(id=photoId)
        if photo:
            return Response(ProfilePhotoSerializer(photo, many=False).data)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @get_photo.mapping.delete
    def remove_photo(self, request, photoId=None):
        profile = self.get_object()
        photo = profile.photos.get(id=photoId)
        if photo and photo.delete():
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @get_photo.mapping.put
    def set_primary(self, request, photoId=None):
        profile = self.get_object()
        photo = profile.photos.get(id=photoId)
        if photo:
            profile.primary_photo = photo
            profile.save()
            all_photos = profile.photos
            photo.order = 0
            photo.save()
            for index, other_photo in enumerate(all_photos.exclude(id=photo.id)):
                other_photo.order = index + 1
                other_photo.save()
            return Response(status=status.HTTP_202_ACCEPTED)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @get_photo.mapping.post
    def replace_photo(self, request, photoId=None):
        profile = self.get_object()
        photo = profile.photos.get(id=photoId)
        if photo:
            photo_file = request.data.get("photo")
            photo.url = photo_file
            photo.approved = False
            photo.rejected_date = None
            photo.rejection_reason = None
            photo.uploaded = datetime.datetime.now(
                datetime.timezone.utc)
            photo.caption = None
            photo.ratio = None
            photo.save()
            return Response(status=status.HTTP_202_ACCEPTED)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def timeline(self, request):
        profile = self.get_object()
        timeline = self.paginate_queryset(profile.visits.select_related("place").filter(
            (Q(valid__isnull=True) | Q(valid=True)), arrival_time__isnull=False).order_by("-arrival_time").all())
        return self.get_paginated_response(PlaceVisitSerializer(timeline, many=True).data)

    @action(detail=False, methods=['get'])
    def visits(self, request):
        profile = self.get_object()
        visits = self.paginate_queryset(profile.places_visited())
        return self.get_paginated_response(PlaceMiniSerializer(visits, many=True).data)

    @action(detail=False, methods=['get'], url_path=r'visits/(?P<visitId>\w+)')
    def get_visit(self, request, visitId=None):
        profile = self.get_object()
        visit = profile.visits.get(hash_id=visitId)
        if visit:
            return Response(PlaceVisitSerializer(visit, many=False).data)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @get_visit.mapping.put
    def update_visit(self, request, visitId=None):
        profile = self.get_object()
        visit = profile.visits.get(hash_id=visitId)
        serializer = UpdatePlaceVisitSerializer(data=request.data)
        if serializer.is_valid():
            visit.arrival_time = serializer.validated_data.get(
                "arrival_time", None)
            visit.departure_time = serializer.validated_data.get(
                "departure_time", None)
            visit.valid = True
            visit.validated = datetime.datetime.now(
                datetime.timezone.utc)
            visit.save()
            return Response(PlaceVisitSerializer(visit, many=False).data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @get_visit.mapping.delete
    def remove_visit(self, request, visitId=None):
        profile = self.get_object()
        visit = profile.visits.get(hash_id=visitId)
        if visit and visit.delete():
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['put'], url_path=r'visits/(?P<visitId>\w+)/validate')
    def validate_visit(self, request, visitId=None):
        profile = self.get_object()
        visit = profile.visits.get(hash_id=visitId)
        if visit:
            visit.valid = True
            visit.validated = datetime.datetime.now(
                datetime.timezone.utc)
            visit.save()
            return Response(status=status.HTTP_202_ACCEPTED)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['put'], url_path=r'visits/(?P<visitId>\w+)/invalidate')
    def invalidate_visit(self, request, visitId=None):
        profile = self.get_object()
        visit = profile.visits.get(hash_id=visitId)
        if visit:
            visit.valid = False
            visit.validated = datetime.datetime.now(
                datetime.timezone.utc)
            visit.save()
            return Response(status=status.HTTP_202_ACCEPTED)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def events(self, request):
        profile = self.get_object()
        events = self.paginate_queryset(profile.get_upcoming_events())
        return self.get_paginated_response(EventInstanceBaseSerializer(events, many=True).data)

    @action(detail=False, methods=['get'], url_path="events/past")
    def past_events(self, request):
        profile = self.get_object()
        events = self.paginate_queryset(profile.get_past_events())
        return self.get_paginated_response(EventInstanceBaseSerializer(events, many=True).data)

    @action(detail=False, methods=['get'], url_path="events/all")
    def all_events(self, request):
        """Returns all events associated with this profile"""
        user = request.user
        profile = self.get_object()
        upcoming_events = profile.get_upcoming_events()
        past_events = self.paginate_queryset(profile.get_past_events())
        favorite_events = profile.favorite_events.select_related('place').prefetch_related('tags', "place__primary_type").filter(
            archived=False).all().order_by('name')
        managed_events = Event.objects.select_related("place", "festival",  "area").prefetch_related(
            "tags", "place__primary_type").filter(manager=user, archived=False).order_by("name").distinct()

        json = {
            "upcoming": EventInstanceBaseSerializer(upcoming_events, many=True).data,
            "past": EventInstanceBaseSerializer(past_events, many=True).data,
        }

        if favorite_events:
            json["favorites"] = EventBaseSerializer(
                favorite_events, many=True).data
        if managed_events:
            json["managed"] = MyEventsSerializer(
                managed_events, many=True).data

        suggested_events = EventsServices.suggested_events(
            profile=profile,
            favorite_events=favorite_events,
            past_events=past_events
        )
        if suggested_events:
            json["suggested"] = EventSuggestionSerializer(
                suggested_events, many=True).data

        return Response(json, status=status.HTTP_200_OK)

    # Fetch all of the events that a user needs to review
    @action(detail=False, methods=['get'], url_path="events/review")
    def events_to_review(self, request):
        profile = self.get_object()
        events_to_review = EventsServices.events_to_review(profile=profile)
        if events_to_review:
            json = {
                "events_to_review": EventInstanceBaseSerializer(events_to_review, many=True, context={"profile": profile, "request": request}).data
            }
            return Response(json, status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'], url_path="check-ins")
    def check_ins(self, request):
        profile = self.get_object()
        check_ins = self.paginate_queryset(profile.check_ins.order_by(
            "-created").prefetch_related("place", "event"))
        return self.get_paginated_response(CheckInSerializer(check_ins, many=True).data)

    @check_ins.mapping.delete
    def remove_check_in(self, request, checkInId=None):
        profile = self.get_object()
        check_in = profile.check_ins.get(hash_id=checkInId)
        if check_in and check_in.delete():
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @check_ins.mapping.put
    def update_check_in(self, request, checkInId=None):
        profile = self.get_object()
        check_in = profile.check_ins.get(hash_id=checkInId)
        serializer = CheckInRequestSerializer(data=request.data)
        if serializer.is_valid():
            check_in.arrival_time = serializer.validated_data.get(
                "arrival_time", check_in.arrival_time)
            check_in.departure_time = serializer.validated_data.get(
                "departure_time", check_in.departure_time)
            check_in.broadcast = serializer.validated_data.get(
                "broadcast", check_in.broadcast)
            check_in.rating = serializer.validated_data.get(
                "rating", check_in.rating)
            check_in.status = serializer.validated_data.get(
                "status", check_in.status)
            check_in.save()
            return Response(CheckInSerializer(check_in, many=False).data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProfileViewSet(viewsets.ModelViewSet):
    permission_classes = (IsAuthenticated, IsAdminUser)
    serializer_classes = {
        'list': ProfileMiniSerializer,
        'retrieve': ProfileSerializer,
        'create': ProfileSerializer,
        'update': ProfileSerializer,
        'partial_update': ProfileSerializer,
        'destroy': ProfileMiniSerializer,
    }
    pagination_class = LongListPagination

    def get_queryset(self):
        return Profile.objects.select_related(
            'user', 'gender_identity', 'sexual_orientation', 'primary_photo'
        ).prefetch_related(
            'photos', 'social_goals'
        ).order_by('created')

    def get_object(self, queryset=None):
        hash_value = self.kwargs.get("pk")
        if hash_value:
            if queryset is None:
                queryset = self.get_queryset()
            return queryset.get(hash_id=hash_value)
        return super().get_object(queryset)

    @transaction.atomic
    def retrieve(self, request, *args, **kwargs):
        profile = self.get_object()
        viewer = request.user.profiles.select_related('user').first()

        if not profile.is_fake and viewer != profile:
            ProfileView.objects.update_or_create(
                profile=profile, viewer=viewer
            )
        return super().retrieve(request, *args, **kwargs)

    @action(detail=False, methods=['post'], serializer_class=UnitRadiusRequestSerializer)
    def nearest(self, request):
        serializer = UnitRadiusRequestSerializer(data=request.data)
        if serializer.is_valid():
            lat = serializer.data.get("lat")
            lng = serializer.data.get("lng")
            distance = serializer.data.get("distance")
            units = serializer.data.get("unit")
            point = Point(lng, lat)
            profiles = ProfileServices.nearest_profiles(
                point=point, distance=distance, unit=units)
            values = ProfileDistancedSerializer(
                profiles, many=True, context={"coordinates": point})
            # Cache key based on location and distance
            cache_key = f"nearest_profiles_{serializer.validated_data['lat']}_{serializer.validated_data['lng']}_{serializer.validated_data['distance']}"
            cached_profiles = cache.get(cache_key)

            if cached_profiles:
                return Response(cached_profiles)

            # ... existing nearest logic ...
            cache.set(cache_key, values.data, 60 * 2)  # Cache for 2 minutes
            return Response(values.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
