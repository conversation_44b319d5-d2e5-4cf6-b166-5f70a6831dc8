from typing import List
from PIL import Image
import os
from uuid import uuid4
from django.db import models
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import User
from django.test import tag
from areas.models import Area
from photos.models import UserUploadedPhoto
from profiles.constants import Ethnicity, SexualPosition, RelationshipStatus
from django.contrib.gis.db import models
from datetime import date
from django_hashids import HashidsField
from places.models import Place
from tags.models import Tag
from django.utils import timezone
from django.db.models import Q, Prefetch


class AcceptedSocials(models.Model):
    name = models.CharField()
    url = models.CharField()
    icon = models.CharField()
    order = models.IntegerField(default=99)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ["order", "name"]


class SocialLink(models.Model):
    type = models.ForeignKey(AcceptedSocials, on_delete=models.CASCADE)
    url = models.CharField()
    object_id = models.PositiveIntegerField()
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, limit_choices_to={
                                     'model__in': ['profile', 'producer', 'dj', 'place']})
    content_object = GenericForeignKey('content_type', 'object_id')

    def __str__(self):
        return self.type.name + " " + self.url


class GenderIdentity(models.Model):
    name = models.CharField()
    common = models.BooleanField(default=False)
    order = models.IntegerField(default=99)

    class Meta:
        ordering = ["order", "name"]
        verbose_name_plural = "Gender Identities"

    def __str__(self):
        return self.name

    @staticmethod
    def find_by_name(name: str):
        return GenderIdentity.objects.filter(name__iexeact=name).first()

    class Meta:
        ordering = ["order", "name"]
        verbose_name_plural = "Gender Identities"


class SexualOrientation(models.Model):
    name = models.CharField()
    common = models.BooleanField(default=False)
    order = models.IntegerField(default=99)

    def __str__(self):
        return self.name

    @staticmethod
    def find_by_name(name: str):
        return SexualOrientation.objects.filter(name__iexeact=name)

    class Meta:
        ordering = ["order", "name"]


class SocialGoal(models.Model):
    name = models.CharField()
    order = models.IntegerField(default=99)

    def __str__(self):
        return self.name

    @staticmethod
    def find_by_name(name: str):
        return SocialGoal.objects.filter(name__iexeact=name).first()

    class Meta:
        ordering = ["order", "name"]


class ProfileManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().prefetch_related("my_tags", "my_interests")


class Profile(models.Model):

    objects = ProfileManager()

    def path_and_rename(instance, filename):
        filename = uuid4().hex
        if instance.is_fake:
            filename = "fake/" + filename
        # return the whole path to the file
        return os.path.join("uploads/profiles", filename)

    hash_id = HashidsField(real_field_name="id")
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="profiles")

    # Basic info
    name = models.CharField()
    dob = models.DateField(null=True, blank=True)
    primary_photo = models.OneToOneField(
        "ProfilePhoto", on_delete=models.SET_NULL, null=True, blank=True, related_name="primary_photo")
    headline = models.CharField(blank=True, null=True)
    details = models.TextField(blank=True, null=True)
    pronouns = models.CharField(blank=True, null=True)

    # Identity Stats
    sexual_orientation = models.ForeignKey(
        SexualOrientation, on_delete=models.SET_NULL, null=True, blank=True)
    gender_identity = models.ForeignKey(
        GenderIdentity, on_delete=models.SET_NULL, null=True, blank=True)

    # Physical Stats
    height_in = models.PositiveSmallIntegerField(blank=True, null=True)
    weight_lbs = models.PositiveSmallIntegerField(blank=True, null=True)
    ethnicity = models.PositiveSmallIntegerField(
        choices=Ethnicity.choices, blank=True, null=True)

    # Social / Intimate Stats
    social_goals = models.ManyToManyField(SocialGoal, blank=True)
    relationship_status = models.PositiveSmallIntegerField(
        choices=RelationshipStatus.choices, blank=True, null=True)
    position = models.PositiveSmallIntegerField(
        choices=SexualPosition.choices, blank=True, null=True)
    socials = models.ManyToManyField("profiles.SocialLink", blank=True)

    # Location info
    area = models.ForeignKey(
        Area, blank=True,  null=True, on_delete=models.SET_NULL)
    home = models.PointField(geography=True, null=True, blank=True)
    home_area = models.ForeignKey(
        Area, blank=True, null=True, on_delete=models.SET_NULL, related_name="home_area")
    work = models.PointField(geography=True, null=True, blank=True)
    work_area = models.ForeignKey(
        Area, blank=True, null=True, on_delete=models.SET_NULL, related_name="work_area")
    school = models.PointField(geography=True, null=True, blank=True)
    school_area = models.ForeignKey(
        Area, blank=True, null=True, on_delete=models.SET_NULL, related_name="school_area")
    coordinates = models.PointField(geography=True, null=True, blank=True)

    # Dwell updates
    last_notification_point = models.PointField(
        geography=True, null=True, blank=True)
    last_notification_timestamp = models.DateTimeField(null=True, blank=True)

    # Events
    events = models.ManyToManyField(
        "events.EventInstance",
        through="events.EventRSVP",
        through_fields=("profile", "event"),
        related_name="attending_profiles",
    )

    def get_upcoming_events(self):
        from events.models import RSVP
        now = timezone.now()
        return self.events.filter(
            (Q(end_time__gte=now) | Q(end_time__isnull=True, start_time__gte=now)),
            rsvps__status__in=[RSVP.INTERESTED, RSVP.GOING],
            rsvps__profile=self,
        ).select_related(
            "parent", "place"
        ).prefetch_related(
            "tags",
            "place__primary_type"
        ).annotate(
            user_rsvp=models.F("rsvps__status")
        ).distinct().order_by("start_time")

    def get_past_events(self):
        from events.models import RSVP
        now = timezone.now()
        return self.events.select_related(
            "parent", "place"
        ).prefetch_related(
            "tags",
            "place__primary_type"
        ).filter(
            rsvps__status__in=[
                RSVP.INTERESTED, RSVP.GOING],
        ).filter(
            models.Q(end_time__lt=now) |
            models.Q(end_time__isnull=True, start_time__lt=now)
        ).annotate(user_rsvp=models.F("rsvps__status")).distinct().order_by("-start_time")

    # Liked things
    favorites = models.ManyToManyField("self", blank=True)
    favorite_places = models.ManyToManyField(
        "places.Place", blank=True, related_name="favorite_places")
    favorite_events = models.ManyToManyField(
        "events.Event", blank=True, related_name="favorite_events")
    favorite_areas = models.ManyToManyField(
        "areas.Area", blank=True, related_name="favorite_areas")
    favorite_producers = models.ManyToManyField(
        "producers.Producer", blank=True)
    favorite_djs = models.ManyToManyField("dj.DJ", blank=True)
    favorite_festivals = models.ManyToManyField(
        "events.Festival", blank=True)

    # Tags / interests
    my_tags = models.ManyToManyField(Tag, related_name="my_tags", blank=True)
    my_interests = models.ManyToManyField(
        Tag, related_name="my_interests", blank=True)

    # Other profiles
    blocked = models.ManyToManyField("self", blank=True)
    pinned = models.ManyToManyField("self", blank=True)
    hidden = models.ManyToManyField("self", blank=True)

    # Flags / Settings
    show_age = models.BooleanField(default=True)
    show_distance = models.BooleanField(default=True)
    is_stealth = models.BooleanField(default=False)
    is_visible = models.BooleanField(default=True)
    is_nsfw = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    is_premium = models.BooleanField(default=False)
    is_fake = models.BooleanField(default=False)
    allow_ai_bio = models.BooleanField(default=True)
    approve_followers = models.BooleanField(default=True)

    def places_visited(self):
        place_ids = self.visits.filter(
            Q(valid=True)).values_list("place__id", flat=True).distinct()
        return Place.objects.filter(id__in=place_ids, approved=True).distinct().order_by("name").prefetch_related("primary_type")

    # Visits
    def recent_visits(self, count: int = 5):
        return self.visits.filter(Q(valid__isnull=True) | Q(valid=True)).filter(arrival_time__isnull=False).order_by("-arrival_time").all()[:count]

    # Stats
    def stats(self):
        from events.models import RSVP
        followers = self.followers.count()
        following = self.following.count()
        favorite_djs = self.favorite_djs.count()
        favorite_producers = self.favorite_producers.count()
        favorite_events = self.favorite_events.count()
        favorite_places = self.favorite_places.count()
        favorite_areas = self.favorite_areas.count()
        check_ins = self.check_ins.count()
        reviews = self.reviews.count()
        rsvps = self.rsvps.filter(
            status__in=[RSVP.INTERESTED, RSVP.GOING]).distinct().count()
        place_visits = self.visits.filter(
            Q(valid=True)).values('place').distinct().count()
        places_added = self.places_added.count()
        return {
            "followers": followers,
            "following": following,
            "favorite_areas": favorite_areas,
            "favorite_places": favorite_places,
            "favorite_djs": favorite_djs,
            "favorite_producers": favorite_producers,
            "favorite_events": favorite_events,
            "check_ins": check_ins,
            "reviews": reviews,
            "place_visits": place_visits,
            "places_added": places_added,
            "rsvps": rsvps,
        }

    # Timestamps
    last_online = models.DateTimeField(blank=True, null=True)
    updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    @property
    def tags(self) -> List[Tag] | None:
        if hasattr(self, '_prefetched_objects_cache') and 'my_tags' in self._prefetched_objects_cache:
            return self._prefetched_objects_cache['my_tags']
        return self.my_tags.all()

    @property
    def tag_ids(self) -> List[int]:
        tag_ids = []
        for tag in self.tags:
            tag_ids.append(tag.id)
        return tag_ids

    @property
    def interests(self) -> List[Tag] | None:
        if hasattr(self, '_prefetched_objects_cache') and 'my_interests' in self._prefetched_objects_cache:
            return self._prefetched_objects_cache['my_interests']
        return self.my_interests.all()

    @property
    def interest_ids(self) -> List[int]:
        tag_ids = []
        for tag in self.interests:
            tag_ids.append(tag.id)
        return tag_ids

    def age(self) -> int:
        if not self.dob:
            return 0
        today = date.today()
        return today.year - self.dob.year - ((today.month, today.day) < (self.dob.month, self.dob.day))

    def __str__(self):
        return self.name

    class Meta:
        ordering = ["-last_online"]


class ProfilePhoto(UserUploadedPhoto):
    url = models.ImageField(null=False, blank=False,
                            upload_to='uploads/profiles/')
    profile = models.ForeignKey(
        Profile, null=False, on_delete=models.CASCADE, related_name="photos")

    ratio = models.FloatField(blank=True, null=True)

    def __str__(self) -> str:
        return self.url.url

    def save(self, *args, **kwargs):
        if self.url:
            try:
                with Image.open(self.url) as img:
                    self.ratio = float(img.width) / float(img.height)
            except Exception as e:
                print(e)
        super().save(*args, **kwargs)

    class Meta:
        ordering = ["order", "-uploaded"]


class Relationship(models.Model):
    follower = models.ForeignKey(
        Profile, related_name='following', on_delete=models.CASCADE)
    followed = models.ForeignKey(
        Profile, related_name='followers', on_delete=models.CASCADE)
    accepted_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('follower', 'followed')
