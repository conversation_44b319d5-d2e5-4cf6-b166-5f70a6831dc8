import datetime
import logging
from django.utils import timezone
from PIL import Image
import os
from uuid import uuid4
from django.db import models
from django_hashids import HashidsField
from announcements.models import Announcement
from photos.models import UserUploadedPhoto
from places.models import Place
from django.dispatch import receiver
from django.db.models.signals import pre_save, post_save
from producers.models import Producer
from django.contrib.auth.models import User
from reviews.models import CheckIn, Review
from tags.models import Tag
import recurrence.fields
from django.db.models import IntegerChoices, Q
from django.core.validators import FileExtensionValidator
from django.contrib.gis.db import models as gis_models

logger = logging.getLogger(__name__)


class Festival(models.Model):
    hash_id = HashidsField(real_field_name="id")
    name = models.CharField(max_length=255)
    start_date = models.DateField()
    end_date = models.DateField()
    area = models.ForeignKey(
        "areas.Area", on_delete=models.SET_NULL, null=True, blank=True)
    place = models.ForeignKey(
        Place, on_delete=models.SET_NULL, null=True, blank=True)
    coordinates = gis_models.PointField(geography=True, null=True, blank=True)
    pride = models.BooleanField(default=False)
    image = models.ImageField(null=True, blank=True,
                              upload_to="uploads/festivals/")
    description = models.TextField(null=True, blank=True)
    published = models.BooleanField(default=False)
    website = models.CharField(max_length=255, null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    producers = models.ManyToManyField(
        Producer, related_name="festivals", blank=True)
    tags = models.ManyToManyField(
        Tag, blank=True, help_text="Tags associated with this festival")

    def announcements(self):
        """Returns all announcements associated with this festival"""
        return Announcement.objects.filter(object_id=self.id, content_type__model="festival")

    def events(self):
        """Returns all upcoming or ongoing events for this festival"""
        return self.event_set.filter(Q(end_date__gte=timezone.now()) | Q(end_date__isnull=True, start_date__gte=timezone.now())).filter(published=True, approved=True).order_by("start_date")

    def upcoming_events(self):
        """Returns all upcoming or ongoing event instances for this festival"""
        return self.eventinstance_set.filter(Q(end_time__gte=timezone.now()) | Q(end_time__isnull=True, start_time__gte=timezone.now())).filter(published=True, approved=True).order_by("start_time")

    def __str__(self):
        return self.name

    class Meta:
        ordering = ["-start_date"]

    def save(self, *args, **kwargs):
        if self.coordinates is None:
            if self.place:
                self.coordinates = self.place.coordinates
            elif self.area:
                self.coordinates = self.area.shape.centroid
        super().save(*args, **kwargs)


class EventDetails(models.Model):

    def path_and_rename(instance, filename):
        filename = uuid4().hex
        return os.path.join("uploads/events", filename)

    def video_path_and_rename(instance, filename):
        filename = uuid4().hex + os.path.splitext(filename)[-1]
        return os.path.join("uploads/events/videos", filename)

    hash_id = HashidsField(real_field_name="id")
    name = models.CharField()
    normalized_name = models.CharField(
        blank=True, null=True, help_text="Used for searching")

    festival = models.ForeignKey(
        Festival, on_delete=models.SET_NULL, null=True, blank=True)

    # Basic Info
    description = models.TextField(
        blank=True, null=True, help_text="Detailed description of the event")
    photo = models.ImageField(blank=True, null=True,
                              upload_to=path_and_rename,
                              help_text="Main image for the event")
    video = models.FileField(blank=True, null=True,
                             upload_to=video_path_and_rename,
                             validators=[FileExtensionValidator(
                                 allowed_extensions=['mp4', 'avi', 'mov', 'mkv', 'webm'])],
                             help_text="Video file for the event (mp4, avi, mov, mkv, webm)")
    photo_ratio = models.FloatField(
        blank=True, null=True, help_text="Aspect ratio of the event photo")
    cost = models.CharField(blank=True, null=True,
                            help_text="Cost of the event")
    is_free = models.BooleanField(
        default=False, help_text="Indicates if the event is free")
    tags = models.ManyToManyField(
        Tag, blank=True, help_text="Tags associated with the event")
    specials = models.TextField(
        blank=True, null=True, help_text="Special offers or details for the event")

    website = models.CharField(
        blank=True, null=True, help_text="Event website URL")
    instagram = models.CharField(
        blank=True, null=True, help_text="Event Instagram handle")
    facebook = models.CharField(
        blank=True, null=True, help_text="Event Facebook page URL")
    twitter = models.CharField(
        blank=True, null=True, help_text="Event Twitter handle")
    ticket_link = models.CharField(
        blank=True, null=True, help_text="URL for purchasing tickets")

    release_date = models.DateField(
        null=True, blank=True, help_text="Date when the event information should be released")
    published = models.BooleanField(
        default=False, help_text="Indicates if the event is published")
    approved = models.BooleanField(
        default=False, help_text="Indicates if the event is approved")
    rejection_date = models.DateTimeField(
        blank=True, null=True, help_text="Date and time when the event was rejected")
    rejection_reason = models.TextField(
        blank=True, null=True, help_text="Reason for event rejection")
    fake = models.BooleanField(
        default=False, help_text="Indicates if the event is fake or not real")

    # Source data
    source = models.CharField(blank=True, null=True,
                              help_text="Source of the event data")
    source_id = models.CharField(
        blank=True, null=True, help_text="ID of the event in the source")
    processed_flyer_data = models.JSONField(
        null=True, blank=True, help_text="Processed flyer data")

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        if self.photo:
            try:
                with Image.open(self.photo) as img:
                    self.photo_ratio = float(img.width) / float(img.height)
            except Exception as e:
                logger.warning(e)
        self.normalized_name = self.name.lower().replace(".", "").replace("!", "").replace("+", " and ").replace(
            "'", "").replace(":", "").replace(",", "").replace("-", " ").replace("&", " and ").replace("  ", " ").strip()
        super().save(*args, **kwargs)


class EventManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related("place", "festival",  "area").prefetch_related("tags", "place__primary_type")


class Event(EventDetails):

    objects = EventManager()
    raw_objects = models.Manager()

    place = models.ForeignKey(
        Place, on_delete=models.CASCADE, null=True, blank=True)
    area = models.ForeignKey(
        "areas.Area", on_delete=models.SET_NULL, null=True, blank=True)

    coordinates = gis_models.PointField(geography=True, null=True, blank=True)

    producers = models.ManyToManyField(
        Producer, related_name="events", blank=True)

    manager = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)

    # Scheduling
    start = models.TimeField(null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end = models.TimeField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    multi_day = models.BooleanField(default=False)
    series = models.BooleanField(default=False)
    recurrences = recurrence.fields.RecurrenceField(null=True, blank=True)

    # Flags
    archived = models.BooleanField(default=False)

    # Timestamps
    updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    def announcements(self):
        """Returns all announcements associated with this event"""
        return Announcement.objects.filter(object_id=self.id, content_type__model="event")

    def upcoming(self):
        return self.events.filter(start_time__gte=timezone.now()).order_by("start_time").select_related("place", "festival",  "area").prefetch_related("tags", "place__primary_type")

    def past(self):
        return self.events.filter(Q(end_time__isnull=False, end_time__lt=timezone.now()) |
                                  Q(end_time__isnull=True, start_time__lt=timezone.now())).order_by("-start_time").select_related("place", "festival",  "area").prefetch_related("tags", "place__primary_type")

    def __str__(self):
        return f"{self.name}"


class EventInstanceQuerySet(models.QuerySet):

    def public(self):
        return self.filter((Q(release_date__isnull=True) | Q(
            release_date__lte=datetime.datetime.now(datetime.timezone.utc))), published=True, approved=True)

    def upcoming(self):
        now = datetime.datetime.now(datetime.timezone.utc)
        return self.filter(
            Q(end_time__isnull=False, end_time__gte=now) |
            Q(end_time__isnull=True, start_time__gte=now)
        )

    def past(self):
        now = datetime.datetime.now(datetime.timezone.utc)
        return self.filter(
            Q(end_time__isnull=False, end_time__lt=now) |
            Q(end_time__isnull=True, start_time__lt=now)
        )


class EventInstanceManager(models.Manager):
    def get_queryset(self):
        return EventInstanceQuerySet(self.model, using=self._db).select_related("place", "festival",  "area", "parent").prefetch_related("tags", "place__primary_type")


class EventInstance(EventDetails):

    objects = EventInstanceManager.from_queryset(
        EventInstanceQuerySet)()

    parent = models.ForeignKey(
        Event, on_delete=models.CASCADE, related_name="events")
    place = models.ForeignKey(
        Place, on_delete=models.SET_NULL, related_name="event_instances", null=True, blank=True)
    area = models.ForeignKey(
        "areas.Area", on_delete=models.SET_NULL, null=True, blank=True)
    coordinates = gis_models.PointField(geography=True, null=True, blank=True)

    producers = models.ManyToManyField(
        Producer, related_name="event_instances", blank=True)

    djs = models.ManyToManyField(
        "dj.DJ", related_name="events", blank=True)

    # Hours / Scheduling
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(blank=True, null=True)
    is_rescheduled = models.BooleanField(default=False)
    cancelled_on = models.DateTimeField(blank=True, null=True)
    hide_end_time = models.BooleanField(default=False)
    is_recurrence = models.BooleanField(default=False)

    # Timestamps
    updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    sold_out = models.BooleanField(default=False)

    def peer_events(self):
        return EventInstance.objects.filter(parent=self.parent).exclude(id=self.id).upcoming()[:5]

    def is_future(self):
        if self.place is not None and self.place.timezone is not None:
            now = datetime.datetime.now(self.place.timezone)
        else:
            now = datetime.datetime.now(datetime.timezone.utc)
        if self.end_time is not None:
            return self.end_time > now
        return self.start_time >= now

    def announcements(self):
        """Returns all announcements associated with this event instance"""
        return Announcement.objects.filter(object_id=self.id, content_type__model="eventinstance")

    def reviews(self):
        return Review.objects.filter(event_instance=self)

    def check_ins(self):
        return CheckIn.objects.filter(event_instance=self)

    def __str__(self):
        if self.place is None or self.place.timezone is None:
            return f"{self.start_time.strftime('%-m/%-d @ %-I:%M%p')} - {self.name}"
        return f"{self.start_time.astimezone(self.place.timezone).strftime('%-m/%-d @ %-I:%M%p')} - {self.name}"

    class Meta:
        ordering = ["start_time"]
        indexes = [
            # Key for user events
            models.Index(fields=['parent', 'start_time']),
            models.Index(fields=['published', 'approved', 'start_time']),
            # For time-based queries
            models.Index(fields=['start_time', 'end_time']),
            models.Index(fields=['parent', 'published', 'approved']),
        ]

    @classmethod
    def upcoming(self):
        now = timezone.now()
        return self.objects.filter(
            models.Q(end_time__gte=now) |
            models.Q(end_time__isnull=True, start_time__gte=now)
        ).filter(
            models.Q(release_date=None) |
            models.Q(release_date__lte=now)
        ).filter(published=True, approved=True).order_by("start_time")

    def past(self):
        now = timezone.now()
        return self.objects.filter(
            models.Q(end_time__lt=now) |
            models.Q(end_time__isnull=True, start_time__lt=now)
        ).filter(
            models.Q(release_date=None) |
            models.Q(release_date__lte=now)
        ).filter(published=True, approved=True).order_by("-start_time")


class EventPhoto(UserUploadedPhoto):
    url = models.ImageField(null=False, upload_to="uploads/events/")
    uploaded_by = models.ForeignKey(
        "profiles.Profile", null=False, on_delete=models.CASCADE)
    event = models.ForeignKey(
        EventInstance, on_delete=models.CASCADE, related_name="photos", null=False)

    def __str__(self) -> str:
        return self.url.url

    def save(self, *args, **kwargs):
        if self.url:
            try:
                with Image.open(self.url) as img:
                    self.ratio = float(img.width) / float(img.height)
            except Exception as e:
                logger.warning(e)
        super().save(*args, **kwargs)


class RSVP(IntegerChoices):
    NOT_GOING = 0,
    INTERESTED = 1,
    GOING = 2,


class EventRSVP(models.Model):
    event = models.ForeignKey(
        EventInstance, on_delete=models.CASCADE, related_name="rsvps")
    status = models.SmallIntegerField(
        choices=RSVP.choices, default=RSVP.INTERESTED)
    profile = models.ForeignKey(
        "profiles.Profile", on_delete=models.CASCADE, related_name="rsvps")

    class Meta:
        unique_together = ["event", "profile"]


class EventFetchMethod(models.TextChoices):
    AI = "ai"
    API = "api"
    SCRAPE = "scrape"
    MANUAL = "manual"


class EventFetch(models.Model):
    event = models.ForeignKey(
        Event, on_delete=models.CASCADE, related_name="fetches", null=True, blank=True)
    place = models.ForeignKey(
        Place, on_delete=models.CASCADE, related_name="event_fetches", null=True, blank=True)
    source = models.CharField(max_length=255)
    method = models.CharField(
        max_length=255, choices=EventFetchMethod.choices, default=EventFetchMethod.MANUAL)
    data = models.JSONField()
    success = models.BooleanField(default=True)
    error = models.TextField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)


@receiver(pre_save, sender=Event)
def update_children(sender, instance: Event, **kwargs):
    try:
        old_event = Event.objects.get(pk=instance.pk)
    except Event.DoesNotExist:
        return None

    updated_children = 0
    within_24_hours = timezone.now() - datetime.timedelta(hours=24)
    children = instance.events.filter(start_time__gte=within_24_hours).all()
    for event_instance in children:
        updated = False
        if instance.name != old_event.name and event_instance.name == old_event.name:
            event_instance.name = instance.name
            updated = True
        if instance.description != old_event.description and event_instance.description == old_event.description:
            event_instance.description = instance.description
            updated = True
        if instance.website != old_event.website and event_instance.website == old_event.website:
            event_instance.website = instance.website
            updated = True
        if instance.instagram != old_event.instagram and event_instance.instagram == old_event.instagram:
            event_instance.instagram = instance.instagram
            updated = True
        if instance.facebook != old_event.facebook and event_instance.facebook == old_event.facebook:
            event_instance.facebook = instance.facebook
            updated = True
        if instance.twitter != old_event.twitter and event_instance.twitter == old_event.twitter:
            event_instance.twitter = instance.twitter
            updated = True
        if instance.cost != old_event.cost and event_instance.cost == old_event.cost:
            event_instance.cost = instance.cost
            updated = True
        if instance.is_free != old_event.is_free and event_instance.is_free == old_event.is_free:
            event_instance.is_free = instance.is_free
            updated = True
        if instance.specials != old_event.specials and event_instance.specials == old_event.specials:
            event_instance.specials = instance.specials
            updated = True
        if instance.place != old_event.place and event_instance.place == old_event.place:
            event_instance.place = instance.place
            updated = True
        if instance.area != old_event.area and event_instance.area == old_event.area:
            event_instance.area = instance.area
            updated = True
        if instance.ticket_link != old_event.ticket_link and event_instance.ticket_link == old_event.ticket_link:
            event_instance.ticket_link = instance.ticket_link
            updated = True

        # For festival attachment
        if instance.festival is not None and event_instance.festival != instance.festival:
            event_instance.festival = instance.festival
            updated = True

        # For tags
        old_tags = set(old_event.tags.values_list('id', flat=True))
        current_child_tags = set(
            event_instance.tags.values_list('id', flat=True))
        new_tags = set(instance.tags.values_list('id', flat=True))
        if instance.tags.exists() and (not current_child_tags or len(current_child_tags) == 0 or (old_tags == current_child_tags and old_tags != new_tags)):
            event_instance.tags.set(instance.tags.all())
            print(f"Updated tags for {event_instance.name}")
            updated = True

        # For producers
        old_producers = set(old_event.producers.values_list('id', flat=True))
        current_child_producers = set(
            event_instance.producers.values_list('id', flat=True))
        new_producers = set(instance.producers.values_list('id', flat=True))
        if hasattr(instance, 'producers') and instance.producers.exists() and old_producers == current_child_producers and old_producers != new_producers:
            event_instance.producers.set(instance.producers.all())
            updated = True

        if updated:
            updated_children += 1
            event_instance.save()

    logger.info(
        f"Updated {updated_children} child events out of {children.count()}")

    # Update child event flags
    if instance.approved == False:
        instance.events.all().update(approved=False)
    if instance.published == False:
        instance.events.all().update(published=False)


@receiver(pre_save, sender=Event)
def update_coordinates(sender, instance: Event, **kwargs):
    if instance.place:
        instance.coordinates = instance.place.coordinates
    elif instance.area:
        instance.coordinates = instance.area.shape.centroid
    else:
        instance.coordinates = None


@receiver(pre_save, sender=EventInstance)
def update_timezones(sender, instance: EventInstance, **kwargs):
    if instance.place is None and instance.area is None:
        return

    if instance.place is not None and instance.place.timezone is not None:
        tz = instance.place.timezone
    elif instance.area is not None and instance.area.timezone is not None:
        tz = instance.area.timezone
    else:
        return

    logger.info(f"Updating timezone to {tz} for {instance}")

    # Always convert end_time to venue timezone
    if instance.end_time is not None:
        instance.end_time = instance.end_time.astimezone(tz)

    # Always convert start_time to venue timezone
    if instance.start_time is not None:
        instance.start_time = instance.start_time.astimezone(tz)

        # If there isn't an end time figure it out based on the venue
        if instance.end_time is None and hasattr(instance.place, 'hours') and instance.place.hours.exists():
            hours = instance.place.hours_on_day(date=instance.start_time)
            venue_close = hours.translated_close(tz=tz)
            if venue_close < instance.start_time:
                venue_close += datetime.timedelta(days=7)
            instance.end_time = venue_close


@receiver(pre_save, sender=EventInstance)
def remove_parent(sender, instance: EventInstance, **kwargs):
    # If the parent event is changing and the old parent only has this one child
    # then remove the parent event and use the parent image for the child if one isn't set
    if instance.pk is not None:
        try:
            old_instance = EventInstance.objects.get(pk=instance.pk)
        except EventInstance.DoesNotExist:
            logger.error(f"Old instance does not exist for {instance}")
            return
        try:
            old_parent = old_instance.parent
            if old_parent is not None and old_parent != instance.parent and old_parent.events.count() == 1:
                if instance.photo is None and old_parent.photo:
                    parent_photo = old_parent.photo
                    instance.photo.save(parent_photo.name, parent_photo)
                old_parent.delete()
                logger.info(f"Deleted parent event {old_parent.name}")
                return
        except Exception as e:
            logger.error(f"Error deleting parent event: {e}")


@receiver(pre_save, sender=EventInstance)
def update_coordinates(sender, instance: EventInstance, **kwargs):
    if instance.place:
        instance.coordinates = instance.place.coordinates
    elif instance.area:
        instance.coordinates = instance.area.shape.centroid
    else:
        instance.coordinates = None
